"""
Module for managing audio and video stream buffering
"""
import asyncio
import time
import logging
from collections import deque
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass
import config

logger = logging.getLogger(__name__)

@dataclass
class BufferChunk:
    """Class for representing a chunk in buffer"""
    data: bytes
    timestamp: float
    chunk_type: str  # 'audio' or 'video'
    sequence_id: int

class AdaptiveBuffer:
    """Adaptive buffer for audio and video synchronization"""
    
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.audio_buffer = deque()
        self.video_buffer = deque()
        
        # Buffer settings
        self.current_buffer_size = config.DEFAULT_BUFFER_SIZE_SECONDS
        self.max_buffer_size = config.MAX_BUFFER_SIZE_SECONDS
        self.adaptive_threshold = config.ADAPTIVE_BUFFER_THRESHOLD_MS / 1000.0

        # Statistics for adaptation
        self.translation_times = deque(maxlen=10)  # Last 10 translation times
        self.last_translation_start = None

        # Counters
        self.audio_sequence = 0
        self.video_sequence = 0

        # Synchronization
        self.start_time = time.time()
        self.is_active = True
        
    async def add_audio_chunk(self, data: bytes, timestamp: float = None) -> int:
        """
        Adds audio chunk to buffer

        Args:
            data: Audio data
            timestamp: Timestamp (optional)

        Returns:
            Sequence ID of added chunk
        """
        if timestamp is None:
            timestamp = time.time() - self.start_time
        
        chunk = BufferChunk(
            data=data,
            timestamp=timestamp,
            chunk_type='audio',
            sequence_id=self.audio_sequence
        )
        
        self.audio_buffer.append(chunk)
        self.audio_sequence += 1
        
        # Limit buffer size
        await self._trim_buffer(self.audio_buffer, 'audio')

        logger.debug(f"Added audio chunk {chunk.sequence_id}, buffer: {len(self.audio_buffer)}")
        return chunk.sequence_id

    async def add_video_chunk(self, data: bytes, timestamp: float = None) -> int:
        """
        Adds video chunk to buffer

        Args:
            data: Video data
            timestamp: Timestamp (optional)

        Returns:
            Sequence ID of added chunk
        """
        if timestamp is None:
            timestamp = time.time() - self.start_time
        
        chunk = BufferChunk(
            data=data,
            timestamp=timestamp,
            chunk_type='video',
            sequence_id=self.video_sequence
        )
        
        self.video_buffer.append(chunk)
        self.video_sequence += 1
        
        # Limit buffer size
        await self._trim_buffer(self.video_buffer, 'video')

        logger.debug(f"Added video chunk {chunk.sequence_id}, buffer: {len(self.video_buffer)}")
        return chunk.sequence_id

    async def get_synchronized_chunks(self, target_timestamp: float) -> Tuple[Optional[BufferChunk], Optional[BufferChunk]]:
        """
        Gets synchronized audio and video chunks for given timestamp

        Args:
            target_timestamp: Target timestamp

        Returns:
            Tuple (audio_chunk, video_chunk) or (None, None) if no data
        """
        audio_chunk = self._find_chunk_by_timestamp(self.audio_buffer, target_timestamp)
        video_chunk = self._find_chunk_by_timestamp(self.video_buffer, target_timestamp)
        
        return audio_chunk, video_chunk
    
    def _find_chunk_by_timestamp(self, buffer: deque, target_timestamp: float) -> Optional[BufferChunk]:
        """Finds chunk closest to given timestamp"""
        if not buffer:
            return None

        best_chunk = None
        min_diff = float('inf')

        for chunk in buffer:
            diff = abs(chunk.timestamp - target_timestamp)
            if diff < min_diff:
                min_diff = diff
                best_chunk = chunk

        return best_chunk

    async def _trim_buffer(self, buffer: deque, buffer_type: str):
        """Trims buffer to maximum size"""
        max_chunks = int(self.current_buffer_size * config.VIDEO_FPS) if buffer_type == 'video' else int(self.current_buffer_size * 20)  # 20 chunks per second for audio

        while len(buffer) > max_chunks:
            removed_chunk = buffer.popleft()
            logger.debug(f"Removed {buffer_type} chunk {removed_chunk.sequence_id} from buffer")

    async def start_translation_timing(self):
        """Starts translation timing"""
        self.last_translation_start = time.time()

    async def end_translation_timing(self):
        """Ends translation timing and adapts buffer"""
        if self.last_translation_start is None:
            return

        translation_time = time.time() - self.last_translation_start
        self.translation_times.append(translation_time)

        # Adapt buffer size
        await self._adapt_buffer_size(translation_time)

        logger.debug(f"Translation time: {translation_time:.2f}s, buffer size: {self.current_buffer_size:.1f}s")
        self.last_translation_start = None

    async def _adapt_buffer_size(self, translation_time: float):
        """Adapts buffer size based on translation time"""
        # If translation takes long, increase buffer
        if translation_time > self.adaptive_threshold:
            new_size = min(
                self.current_buffer_size + 0.5,
                self.max_buffer_size
            )
            if new_size != self.current_buffer_size:
                self.current_buffer_size = new_size
                logger.info(f"Increased buffer to {self.current_buffer_size:.1f}s due to slow translation")

        # If translation is fast and buffer is large, decrease it
        elif translation_time < self.adaptive_threshold / 2 and self.current_buffer_size > config.DEFAULT_BUFFER_SIZE_SECONDS:
            avg_translation_time = sum(self.translation_times) / len(self.translation_times)
            if avg_translation_time < self.adaptive_threshold / 2:
                new_size = max(
                    self.current_buffer_size - 0.2,
                    config.DEFAULT_BUFFER_SIZE_SECONDS
                )
                if new_size != self.current_buffer_size:
                    self.current_buffer_size = new_size
                    logger.info(f"Decreased buffer to {self.current_buffer_size:.1f}s due to fast translation")
    
    async def get_buffer_status(self) -> Dict[str, Any]:
        """Returns buffer status"""
        return {
            "session_id": self.session_id,
            "audio_buffer_size": len(self.audio_buffer),
            "video_buffer_size": len(self.video_buffer),
            "current_buffer_seconds": self.current_buffer_size,
            "avg_translation_time": sum(self.translation_times) / len(self.translation_times) if self.translation_times else 0,
            "is_active": self.is_active
        }

    async def clear_buffers(self):
        """Clears all buffers"""
        self.audio_buffer.clear()
        self.video_buffer.clear()
        self.translation_times.clear()
        logger.info(f"Cleared buffers for session {self.session_id}")

    async def stop(self):
        """Stops buffer"""
        self.is_active = False
        await self.clear_buffers()
        logger.info(f"Stopped buffer for session {self.session_id}")

class BufferManager:
    """Manager for handling multiple buffers"""

    def __init__(self):
        self.buffers: Dict[str, AdaptiveBuffer] = {}

    async def create_buffer(self, session_id: str) -> AdaptiveBuffer:
        """Creates new buffer for session"""
        if session_id in self.buffers:
            await self.buffers[session_id].stop()

        buffer = AdaptiveBuffer(session_id)
        self.buffers[session_id] = buffer
        logger.info(f"Created buffer for session {session_id}")
        return buffer

    async def get_buffer(self, session_id: str) -> Optional[AdaptiveBuffer]:
        """Gets buffer for session"""
        return self.buffers.get(session_id)

    async def remove_buffer(self, session_id: str):
        """Removes buffer for session"""
        if session_id in self.buffers:
            await self.buffers[session_id].stop()
            del self.buffers[session_id]
            logger.info(f"Removed buffer for session {session_id}")

    async def get_all_statuses(self) -> Dict[str, Dict[str, Any]]:
        """Returns statuses of all buffers"""
        statuses = {}
        for session_id, buffer in self.buffers.items():
            statuses[session_id] = await buffer.get_buffer_status()
        return statuses

# Global buffer manager
buffer_manager = BufferManager()
