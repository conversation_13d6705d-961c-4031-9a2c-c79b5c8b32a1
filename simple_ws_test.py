#!/usr/bin/env python3
"""
Simple WebSocket test to debug connection issues
"""
import asyncio
import websockets
import json

async def simple_test():
    uri = "ws://localhost:8000/ws/en"
    print(f"Connecting to {uri}...")
    
    try:
        async with websockets.connect(uri, ping_interval=None, ping_timeout=None) as websocket:
            print("✓ Connected!")
            
            # Send a simple test message
            test_message = {"type": "test", "data": "hello"}
            await websocket.send(json.dumps(test_message))
            print("✓ Sent test message")
            
            # Try to receive a response
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"✓ Received: {response}")
            except asyncio.TimeoutError:
                print("⚠ No response (timeout)")
            
            print("✓ Connection test successful!")
            
    except Exception as e:
        print(f"✗ Connection failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(simple_test()) 