"""
Session Management for Live Translation

Handles session lifecycle, resource management, and cleanup for translation sessions.
"""
import logging
import time
from typing import Dict, Optional
from dataclasses import dataclass, field
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class SessionStats:
    """Statistics for a translation session"""
    session_id: str
    start_time: float
    target_language: str
    source_language: str = "auto"
    audio_chunks_processed: int = 0
    transcriptions_count: int = 0
    translations_count: int = 0
    audio_output_count: int = 0
    total_audio_bytes: int = 0
    total_translated_audio_bytes: int = 0
    errors_count: int = 0
    last_activity: float = field(default_factory=time.time)
    
    @property
    def duration(self) -> float:
        """Get session duration in seconds"""
        return time.time() - self.start_time
    
    @property
    def is_active(self) -> bool:
        """Check if session is still active (activity within last 30 seconds)"""
        return (time.time() - self.last_activity) < 30.0


class SessionManager:
    """Manages translation sessions and their lifecycle"""
    
    def __init__(self):
        self.sessions: Dict[str, SessionStats] = {}
        self._cleanup_interval = 300  # 5 minutes
        self._last_cleanup = time.time()
    
    def create_session(self, session_id: str, target_language: str, source_language: str = "auto") -> SessionStats:
        """Create a new session"""
        session_stats = SessionStats(
            session_id=session_id,
            start_time=time.time(),
            target_language=target_language,
            source_language=source_language
        )
        self.sessions[session_id] = session_stats
        logger.info(f"Created session {session_id} for {source_language} -> {target_language}")
        return session_stats
    
    def get_session(self, session_id: str) -> Optional[SessionStats]:
        """Get session statistics"""
        return self.sessions.get(session_id)
    
    def update_session_activity(self, session_id: str):
        """Update last activity timestamp for a session"""
        if session_id in self.sessions:
            self.sessions[session_id].last_activity = time.time()
    
    def record_audio_chunk(self, session_id: str, chunk_size: int):
        """Record audio chunk processing"""
        if session_id in self.sessions:
            session = self.sessions[session_id]
            session.audio_chunks_processed += 1
            session.total_audio_bytes += chunk_size
            session.last_activity = time.time()
    
    def record_transcription(self, session_id: str):
        """Record transcription event"""
        if session_id in self.sessions:
            session = self.sessions[session_id]
            session.transcriptions_count += 1
            session.last_activity = time.time()
    
    def record_translation(self, session_id: str):
        """Record translation event"""
        if session_id in self.sessions:
            session = self.sessions[session_id]
            session.translations_count += 1
            session.last_activity = time.time()
    
    def record_audio_output(self, session_id: str, audio_size: int):
        """Record audio output event"""
        if session_id in self.sessions:
            session = self.sessions[session_id]
            session.audio_output_count += 1
            session.total_translated_audio_bytes += audio_size
            session.last_activity = time.time()
    
    def record_error(self, session_id: str):
        """Record error event"""
        if session_id in self.sessions:
            session = self.sessions[session_id]
            session.errors_count += 1
            session.last_activity = time.time()
    
    def end_session(self, session_id: str) -> Optional[SessionStats]:
        """End a session and return final statistics"""
        session = self.sessions.pop(session_id, None)
        if session:
            logger.info(f"Ended session {session_id} after {session.duration:.1f}s")
            logger.info(f"Session stats: {session.transcriptions_count} transcriptions, "
                       f"{session.translations_count} translations, "
                       f"{session.audio_output_count} audio outputs, "
                       f"{session.errors_count} errors")
        return session
    
    def get_active_sessions(self) -> Dict[str, SessionStats]:
        """Get all active sessions"""
        self._cleanup_inactive_sessions()
        return {sid: stats for sid, stats in self.sessions.items() if stats.is_active}
    
    def get_total_stats(self) -> dict:
        """Get aggregated statistics for all sessions"""
        active_sessions = self.get_active_sessions()
        
        if not active_sessions:
            return {
                "active_sessions": 0,
                "total_sessions": len(self.sessions),
                "total_audio_chunks": 0,
                "total_transcriptions": 0,
                "total_translations": 0,
                "total_audio_outputs": 0,
                "total_errors": 0,
                "average_session_duration": 0.0
            }
        
        total_chunks = sum(s.audio_chunks_processed for s in active_sessions.values())
        total_transcriptions = sum(s.transcriptions_count for s in active_sessions.values())
        total_translations = sum(s.translations_count for s in active_sessions.values())
        total_audio_outputs = sum(s.audio_output_count for s in active_sessions.values())
        total_errors = sum(s.errors_count for s in active_sessions.values())
        avg_duration = sum(s.duration for s in active_sessions.values()) / len(active_sessions)
        
        return {
            "active_sessions": len(active_sessions),
            "total_sessions": len(self.sessions),
            "total_audio_chunks": total_chunks,
            "total_transcriptions": total_transcriptions,
            "total_translations": total_translations,
            "total_audio_outputs": total_audio_outputs,
            "total_errors": total_errors,
            "average_session_duration": avg_duration
        }
    
    def _cleanup_inactive_sessions(self):
        """Clean up inactive sessions periodically"""
        current_time = time.time()
        if current_time - self._last_cleanup < self._cleanup_interval:
            return
        
        inactive_sessions = [
            sid for sid, stats in self.sessions.items()
            if not stats.is_active
        ]
        
        for session_id in inactive_sessions:
            logger.info(f"Cleaning up inactive session: {session_id}")
            self.end_session(session_id)
        
        self._last_cleanup = current_time


# Global session manager instance
session_manager = SessionManager()
