"""
Database Models for the Omnispeak Backend

SQLModel-based database models for hosts, rooms, guests, and translation sessions.
"""
from typing import Optional
from sqlmodel import Field, SQLModel


class Host(SQLModel, table=True):
    """Host model for room creators"""
    id: Optional[int] = Field(default=None, primary_key=True)
    full_name: str
    preferred_language: str
    voice_sample_url: Optional[str] = None


class Room(SQLModel, table=True):
    """Room model for translation sessions"""
    id: Optional[int] = Field(default=None, primary_key=True)
    host_id: int = Field(foreign_key="host.id")
    title: str
    host_language: str
    max_languages: int = 1
    status: str = "created"  # created, live, ended


class Guest(SQLModel, table=True):
    """Guest model for room participants"""
    id: Optional[int] = Field(default=None, primary_key=True)
    display_name: Optional[str] = None
    room_id: int = Field(foreign_key="room.id")


class RoomLanguage(SQLModel, table=True):
    """Language configuration for rooms"""
    id: Optional[int] = Field(default=None, primary_key=True)
    room_id: int = Field(foreign_key="room.id")
    language: str
    stream_url: Optional[str] = None
    cloudflare_video_uid: Optional[str] = None
    status: str = "pending"  # pending, processing, ready, error


class TranslationSession(SQLModel, table=True):
    """Translation session tracking"""
    id: Optional[int] = Field(default=None, primary_key=True)
    session_id: str = Field(unique=True)
    room_id: int = Field(foreign_key="room.id")
    target_language: str
    original_audio_path: Optional[str] = None
    original_video_path: Optional[str] = None
    translated_audio_path: Optional[str] = None
    final_video_path: Optional[str] = None
    cloudflare_video_uid: Optional[str] = None
    status: str = "active"  # active, completed, error
    created_at: Optional[str] = None
    completed_at: Optional[str] = None
