# Got from prompt:
# I need to implement two classes: for chat message and for a database. Chat message should have the following fields: text, sender_id, timestamp, room_id. sender_id and room_id are GUIDs. Database class should save all the messages into a SQLite DB chat.db and provide several methods to work with. First, it should provide a method to save a new message into DB. Next there should be a method to read all the messages by room it, ordered in sequence from oldest to newest. There also should be a hook to be triggered when a messsage with a specified room ID was added

import sqlite3
from dataclasses import dataclass
from datetime import datetime
from typing import List, Callable, Optional
import uuid
import logging

@dataclass
class ChatMessage:
    text: str
    sender_id: str  # GUID as string
    timestamp: datetime
    room_id: str    # GUID as string

class ChatDatabase:
    def __init__(self, db_path: str = "chat.db"):
        self.db_path = db_path
        self._create_table()

    def _create_table(self):
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS messages (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    text TEXT NOT NULL,
                    sender_id TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    room_id TEXT NOT NULL
                )
            ''')

    def save_message(self, message: ChatMessage):
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                INSERT INTO messages (text, sender_id, timestamp, room_id)
                VALUES (?, ?, ?, ?)
            ''', (message.text, message.sender_id, message.timestamp.isoformat(), message.room_id))
            logging.info(f"Message saved: {message}")

    def get_messages_by_room(self, room_id: str) -> List[ChatMessage]:
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute('''
                SELECT text, sender_id, timestamp, room_id
                FROM messages
                WHERE room_id = ?
                ORDER BY timestamp ASC
            ''', (room_id,))
            rows = cursor.fetchall()
        return [
            ChatMessage(
                text=row[0],
                sender_id=row[1],
                timestamp=datetime.fromisoformat(row[2]),
                room_id=row[3]
            )
            for row in rows
        ]


from fastapi import FastAPI, Request, HTTPException, Depends
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import jwt
from jwt.exceptions import PyJWTError
from src.utils import config

class ChatMessageInc(BaseModel):
    text: str
    room_id: str

def get_listener_id_from_jwt(request: Request) -> str:
    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Missing or invalid Authorization header")
    token = auth_header.split(" ", 1)[1]
    try:
        payload = jwt.decode(token, config.JWT_SECRET, algorithms=[config.JWT_ALGO])
        listener_id = payload.get("listener_id")
        if not listener_id:
            raise HTTPException(status_code=401, detail="listener_id missing in token")
        return listener_id
    except PyJWTError as e:
        logging.error(f"Error decoding JWT token: {e}")
        raise HTTPException(status_code=401, detail="Invalid JWT token")