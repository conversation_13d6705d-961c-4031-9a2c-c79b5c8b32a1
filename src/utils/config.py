"""
Configuration for live translation system
"""
import os
from typing import Optional
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Gemini API
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "AIzaSyCtWUcjDH-PaE3CuZuwDEzPnfWPDC_oQZk")
GEMINI_MODEL = "gemini-2.5-flash-preview-native-audio-dialog"

# OpenAI API (for Whisper STT)
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")

# Gladia API (for real-time STT)
GLADIA_API_KEY = os.getenv("GLADIA_API_KEY")
GLADIA_BASE_URL = "wss://api.gladia.io/audio/text/audio-transcription"

JWT_SECRET = os.getenv("JWT_SECRET", "5iHid82PJTmCbV06na5Yif2eXWv221oj")
JWT_ALGO = "HS256"

# Cloudflare Stream API
CLOUDFLARE_API_TOKEN = os.getenv("CLOUDFLARE_API_TOKEN", "")
CLOUDFLARE_ACCOUNT_ID = os.getenv("CLOUDFLARE_ACCOUNT_ID", "")
CLOUDFLARE_STREAM_API_BASE = "https://api.cloudflare.com/client/v4"

# WebSocket server
SERVER_HOST = "0.0.0.0"
SERVER_PORT = 8766

# Audio settings
INPUT_SAMPLE_RATE = 16000
OUTPUT_SAMPLE_RATE = 24000
AUDIO_CHUNK_SIZE_MS = 50

# Video settings
VIDEO_FPS = 30
VIDEO_BUFFER_SECONDS = 5  # Buffer video for 5 seconds
VIDEO_CODEC = "libx264"
AUDIO_CODEC = "aac"

# File paths
TEMP_DIR = "./temp"
RECORDINGS_DIR = "./recordings"
ORIGINAL_AUDIO_DIR = "./recordings/original_audio"
ORIGINAL_VIDEO_DIR = "./recordings/original_video"
TRANSLATED_AUDIO_DIR = "./recordings/translated_audio"
FINAL_VIDEO_DIR = "./recordings/final_video"

# Buffering
DEFAULT_BUFFER_SIZE_SECONDS = 3
MAX_BUFFER_SIZE_SECONDS = 10
ADAPTIVE_BUFFER_THRESHOLD_MS = 2000  # If translation takes more than 2 sec, increase buffer

# Pipeline optimization settings
PIPELINE_LATENCY_MODE = "balanced"  # "low_latency", "balanced", "high_quality"

# STT (Speech-to-Text) settings
STT_CHUNK_DURATION_MS = 100  # Duration of audio chunks sent to STT
STT_INTERIM_RESULTS = True
STT_LANGUAGE_DETECTION = True
STT_ENHANCED_MODEL = True

# Sentence detection settings
SENTENCE_TIMEOUT_SECONDS = 3.0  # Force emit sentence after this timeout
MAX_CONTEXT_SENTENCES = 3  # Number of previous sentences to use for context
MIN_SENTENCE_LENGTH = 3  # Minimum characters for a sentence
PUNCTUATION_TIMEOUT_MS = 1500  # Wait time after punctuation before emitting

# Translation settings
TRANSLATION_TEMPERATURE = 0.1  # Low temperature for consistent translations
TRANSLATION_MAX_TOKENS = 200
TRANSLATION_TIMEOUT_SECONDS = 10.0
BATCH_TRANSLATION_SIZE = 1  # Number of sentences to batch (1 = no batching for low latency)

# TTS (Text-to-Speech) settings
TTS_SPEAKING_RATE = 1.0  # Normal speaking rate
TTS_VOICE_GENDER = "NEUTRAL"  # "NEUTRAL", "MALE", "FEMALE"
TTS_MAX_CONCURRENT = 3  # Maximum concurrent TTS requests
TTS_TIMEOUT_SECONDS = 15.0
TTS_CHUNK_SIZE_MS = 500  # Size of audio chunks for streaming

# Latency optimization presets
LATENCY_PRESETS = {
    "low_latency": {
        "sentence_timeout": 2.0,
        "max_context_sentences": 1,
        "punctuation_timeout_ms": 1000,
        "translation_timeout": 5.0,
        "batch_translation_size": 1,
        "tts_max_concurrent": 2,
        "stt_chunk_duration_ms": 50,
    },
    "balanced": {
        "sentence_timeout": 3.0,
        "max_context_sentences": 3,
        "punctuation_timeout_ms": 1500,
        "translation_timeout": 10.0,
        "batch_translation_size": 1,
        "tts_max_concurrent": 3,
        "stt_chunk_duration_ms": 100,
    },
    "high_quality": {
        "sentence_timeout": 5.0,
        "max_context_sentences": 5,
        "punctuation_timeout_ms": 2000,
        "translation_timeout": 15.0,
        "batch_translation_size": 3,
        "tts_max_concurrent": 4,
        "stt_chunk_duration_ms": 200,
    }
}

# Apply latency preset
def apply_latency_preset(preset_name: str = None):
    """Apply a latency optimization preset"""
    global SENTENCE_TIMEOUT_SECONDS, MAX_CONTEXT_SENTENCES, PUNCTUATION_TIMEOUT_MS
    global TRANSLATION_TIMEOUT_SECONDS, BATCH_TRANSLATION_SIZE, TTS_MAX_CONCURRENT
    global STT_CHUNK_DURATION_MS
    
    preset_name = preset_name or PIPELINE_LATENCY_MODE
    if preset_name not in LATENCY_PRESETS:
        return
        
    preset = LATENCY_PRESETS[preset_name]
    
    SENTENCE_TIMEOUT_SECONDS = preset["sentence_timeout"]
    MAX_CONTEXT_SENTENCES = preset["max_context_sentences"]
    PUNCTUATION_TIMEOUT_MS = preset["punctuation_timeout_ms"]
    TRANSLATION_TIMEOUT_SECONDS = preset["translation_timeout"]
    BATCH_TRANSLATION_SIZE = preset["batch_translation_size"]
    TTS_MAX_CONCURRENT = preset["tts_max_concurrent"]
    STT_CHUNK_DURATION_MS = preset["stt_chunk_duration_ms"]

# External service settings
USE_GLADIA_STT = True  # Use Gladia for real-time STT (preferred)
USE_OPENAI_WHISPER = False  # Use OpenAI Whisper for STT (batch processing)
GOOGLE_STT_ENABLED = False  # Use Google Cloud Speech-to-Text if available
GOOGLE_TTS_ENABLED = True  # Use Google Cloud Text-to-Speech if available
FALLBACK_TO_GEMINI = True  # Fallback to Gemini for STT/TTS if other services unavailable

# Performance monitoring
ENABLE_PERFORMANCE_LOGGING = True
PERFORMANCE_LOG_INTERVAL_SECONDS = 30  # Log performance stats every 30 seconds
LATENCY_WARNING_THRESHOLD_MS = 5000  # Warn if end-to-end latency exceeds this

# Quality settings
AUDIO_QUALITY_BITRATE = 128  # kbps for output audio
VIDEO_QUALITY_BITRATE = 2000  # kbps for output video
ENABLE_NOISE_SUPPRESSION = True
ENABLE_ECHO_CANCELLATION = True

# Logging
LOG_LEVEL = "DEBUG"
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# Limits
MAX_STREAM_DURATION_HOURS = 8
MAX_VIDEO_BITRATE_KBPS = 5000
MAX_AUDIO_BITRATE_KBPS = 128
MAX_CONCURRENT_SESSIONS = 10  # Maximum number of concurrent translation sessions

def ensure_directories():
    """Creates necessary directories if they don't exist"""
    import os
    directories = [
        TEMP_DIR,
        RECORDINGS_DIR,
        ORIGINAL_AUDIO_DIR,
        ORIGINAL_VIDEO_DIR,
        TRANSLATED_AUDIO_DIR,
        FINAL_VIDEO_DIR
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)

def get_pipeline_config(target_language: str, source_language: str = "auto", 
                       latency_mode: str = None) -> dict:
    """Get optimized pipeline configuration for a language pair"""
    # Apply latency preset if specified
    if latency_mode:
        apply_latency_preset(latency_mode)
    
    return {
        "target_language": target_language,
        "source_language": source_language,
        "max_context_sentences": MAX_CONTEXT_SENTENCES,
        "sentence_timeout": SENTENCE_TIMEOUT_SECONDS,
        "use_gladia_stt": USE_GLADIA_STT,
        "use_openai_whisper": USE_OPENAI_WHISPER,
        "use_google_stt": GOOGLE_STT_ENABLED,
        "use_google_tts": GOOGLE_TTS_ENABLED,
        "max_concurrent_tts": TTS_MAX_CONCURRENT,
        "voice_gender": TTS_VOICE_GENDER,
        "speaking_rate": TTS_SPEAKING_RATE,
        "translation_timeout": TRANSLATION_TIMEOUT_SECONDS,
        "batch_size": BATCH_TRANSLATION_SIZE,
        "stt_chunk_duration_ms": STT_CHUNK_DURATION_MS,
        "enable_performance_logging": ENABLE_PERFORMANCE_LOGGING
    }

# Apply default latency preset on import
apply_latency_preset()
