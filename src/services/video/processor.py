"""
Module for video processing and audio synchronization
"""
import asyncio
import cv2
import ffmpeg
import numpy as np
import logging
import time
from pathlib import Path
from typing import Optional, Tuple, List
from src.utils import config

logger = logging.getLogger(__name__)

class VideoProcessor:
    """Class for processing video streams"""

    def __init__(self, session_id: str):
        self.session_id = session_id
        self.video_buffer = []
        self.video_timestamps = []
        self.is_recording = False
        self.start_time = None

        # File paths
        self.original_video_path = Path(config.ORIGINAL_VIDEO_DIR) / f"{session_id}_original.mp4"
        self.temp_video_path = Path(config.TEMP_DIR) / f"{session_id}_temp.mp4"
        self.final_video_path = Path(config.FINAL_VIDEO_DIR) / f"{session_id}_final.mp4"

        # Video settings
        self.fps = config.VIDEO_FPS
        self.video_writer = None
        
    async def start_recording(self, width: int = 1280, height: int = 720):
        """
        Starts video recording

        Args:
            width: Video width
            height: Video height
        """
        try:
            # Create VideoWriter for saving original video
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            self.video_writer = cv2.VideoWriter(
                str(self.original_video_path),
                fourcc,
                self.fps,
                (width, height)
            )

            self.is_recording = True
            self.start_time = time.time()
            logger.info(f"Started video recording: {self.original_video_path}")

        except Exception as e:
            logger.error(f"Error starting video recording: {e}")
            raise
    
    async def add_video_frame(self, frame_data: bytes, timestamp: float = None):
        """
        Adds video frame to buffer and file

        Args:
            frame_data: Frame data in bytes
            timestamp: Frame timestamp
        """
        if not self.is_recording:
            return

        try:
            # Convert bytes to numpy array
            nparr = np.frombuffer(frame_data, np.uint8)
            frame = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

            if frame is None:
                logger.warning("Failed to decode video frame")
                return

            # Add to buffer
            current_time = timestamp or (time.time() - self.start_time)
            self.video_buffer.append(frame.copy())
            self.video_timestamps.append(current_time)

            # Write to file
            if self.video_writer:
                self.video_writer.write(frame)

            # Limit buffer size
            max_buffer_frames = int(self.fps * config.VIDEO_BUFFER_SECONDS)
            if len(self.video_buffer) > max_buffer_frames:
                self.video_buffer.pop(0)
                self.video_timestamps.pop(0)

        except Exception as e:
            logger.error(f"Error adding video frame: {e}")
    
    async def stop_recording(self):
        """Stops video recording"""
        try:
            self.is_recording = False

            if self.video_writer:
                self.video_writer.release()
                self.video_writer = None

            logger.info(f"Stopped video recording: {self.original_video_path}")

        except Exception as e:
            logger.error(f"Error stopping video recording: {e}")

    async def merge_with_audio(self, audio_path: Path, output_path: Path = None) -> Path:
        """
        Merges video with new audio track

        Args:
            audio_path: Path to audio file
            output_path: Path for output file (optional)

        Returns:
            Path to merged video file
        """
        if output_path is None:
            output_path = self.final_video_path
        
        try:
            if not self.original_video_path.exists():
                raise FileNotFoundError(f"Original video not found: {self.original_video_path}")

            if not audio_path.exists():
                raise FileNotFoundError(f"Audio file not found: {audio_path}")

            # Use ffmpeg for merging
            video_input = ffmpeg.input(str(self.original_video_path))
            audio_input = ffmpeg.input(str(audio_path))

            # Merge settings
            output = ffmpeg.output(
                video_input['v'],  # video stream
                audio_input['a'],  # audio stream
                str(output_path),
                vcodec=config.VIDEO_CODEC,
                acodec=config.AUDIO_CODEC,
                **{
                    'b:v': f'{config.MAX_VIDEO_BITRATE_KBPS}k',
                    'b:a': f'{config.MAX_AUDIO_BITRATE_KBPS}k',
                    'shortest': None  # trim to shortest stream
                }
            )

            # Execute merge
            await asyncio.to_thread(ffmpeg.run, output, overwrite_output=True, quiet=True)

            logger.info(f"Video merged with audio: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"Error merging video with audio: {e}")
            raise
    
    async def get_video_duration(self, video_path: Path) -> float:
        """
        Gets video duration in seconds

        Args:
            video_path: Path to video file

        Returns:
            Duration in seconds
        """
        try:
            probe = await asyncio.to_thread(
                ffmpeg.probe, str(video_path)
            )
            duration = float(probe['streams'][0]['duration'])
            return duration
        except Exception as e:
            logger.error(f"Error getting video duration: {e}")
            return 0.0

    async def extract_audio_from_video(self, video_path: Path, audio_path: Path):
        """
        Extracts audio from video file

        Args:
            video_path: Path to video file
            audio_path: Path to save audio
        """
        try:
            input_video = ffmpeg.input(str(video_path))
            audio = input_video.audio

            output = ffmpeg.output(
                audio,
                str(audio_path),
                acodec='pcm_s16le',  # PCM 16-bit for Gemini compatibility
                ar=config.INPUT_SAMPLE_RATE,
                ac=1  # mono
            )

            await asyncio.to_thread(ffmpeg.run, output, overwrite_output=True, quiet=True)
            logger.info(f"Audio extracted from video: {audio_path}")

        except Exception as e:
            logger.error(f"Error extracting audio from video: {e}")
            raise

    def cleanup(self):
        """Cleans up temporary files"""
        try:
            if self.temp_video_path.exists():
                self.temp_video_path.unlink()

            # Clear buffer
            self.video_buffer.clear()
            self.video_timestamps.clear()

        except Exception as e:
            logger.error(f"Error cleaning up temporary files: {e}")
