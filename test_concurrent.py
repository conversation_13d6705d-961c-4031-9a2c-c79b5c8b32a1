#!/usr/bin/env python3
"""
Test concurrent connections to verify rate limiting
"""
import asyncio
import websockets
import json

async def test_connection(connection_id):
    uri = "ws://localhost:8001/ws/en"
    print(f"Connection {connection_id}: Attempting to connect...")
    
    try:
        async with websockets.connect(uri) as websocket:
            print(f"Connection {connection_id}: ✓ Connected successfully!")
            
            # Send join-room message
            await websocket.send(json.dumps({
                "type": "join-room",
                "roomId": "test-room",
                "role": "preacher"
            }))
            
            # Wait a bit
            await asyncio.sleep(5)
            print(f"Connection {connection_id}: Closing...")
            
    except Exception as e:
        print(f"Connection {connection_id}: ✗ Failed: {e}")

async def main():
    print("Testing concurrent connections...")
    
    # Start multiple connections simultaneously
    tasks = [
        test_connection(1),
        test_connection(2),
        test_connection(3)
    ]
    
    await asyncio.gather(*tasks, return_exceptions=True)
    print("Concurrent test completed!")

if __name__ == "__main__":
    asyncio.run(main())
