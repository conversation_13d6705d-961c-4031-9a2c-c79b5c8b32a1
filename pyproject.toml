[project]
name = "live-translations-api"
version = "0.1.0"
description = "Live video translation service with Gemini AI and Cloudflare Stream"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "fastapi",
    "uvicorn[standard]",
    "sqlmodel",
    "psycopg2-binary",
    "websockets",
    "google-genai",
    "google-cloud-speech",
    "google-cloud-texttospeech",
    "soundfile",
    "opencv-python",
    "ffmpeg-python",
    "requests",
    "numpy>=1.24.0",
    "aiohttp",
    "python-multipart",
    "pydub",
    "pyjwt>=2.10.1",
    "scalar-fastapi",
    "python-dotenv",
]
