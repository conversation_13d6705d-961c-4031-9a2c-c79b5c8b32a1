# Live Translation System

A real-time video and audio translation system using Gemini AI and Cloudflare Stream. This system receives live audio and video streams via WebSocket, translates the audio using Google's Gemini API, and outputs synchronized video with translated audio.

## Features

- **Real-time Audio Translation**: Translates live audio streams using Gemini AI
- **Video Processing**: Handles video streams and synchronizes with translated audio
- **Adaptive Buffering**: Automatically adjusts buffer size based on translation performance
- **Cloudflare Stream Integration**: Uploads final videos to Cloudflare Stream
- **Multiple Language Support**: Supports translation to various languages
- **WebSocket Protocol**: Real-time communication via WebSocket connections

## Architecture

### Core Components

1. **WebSocket Server** (`mvp.py`): Main server handling client connections
2. **Video Processor** (`video_processor.py`): Handles video recording and merging
3. **Buffer Manager** (`buffer_manager.py`): Manages audio/video synchronization
4. **Cloudflare Stream Client** (`cloudflare_stream.py`): Handles video uploads
5. **Configuration** (`config.py`): System configuration and settings

### Data Flow

```
Client → WebSocket → Audio/Video Processing → Gemini Translation → Video Merging → Cloudflare Stream
```

## Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd omnispeak-backend
   ```

2. **Install dependencies**

   ```bash
   uv sync
   ```

3. **Set up environment variables**
   ```bash
   export GEMINI_API_KEY="your_gemini_api_key"
   export CLOUDFLARE_API_TOKEN="your_cloudflare_token"  # Optional
   export CLOUDFLARE_ACCOUNT_ID="your_account_id"      # Optional
   ```

## Usage

### Starting the Server

```bash
uv run python mvp.py
```

The server will start on `ws://localhost:8765` by default.

### WebSocket Endpoints

- `/English` - Translate to English
- `/Spanish` - Translate to Spanish
- `/French` - Translate to French
- `/German` - Translate to German
- Add more languages as needed

### Client Protocol

#### Audio Data

Send raw PCM_16 audio data (16kHz, mono) as binary messages.

#### Video Data

Send video frames as JPEG-encoded binary data.

#### Control Messages

Send JSON messages for control:

```json
{
  "type": "start_recording",
  "width": 1280,
  "height": 720
}
```

```json
{
  "type": "stop_recording"
}
```

### Testing the Client

```bash
uv run python mvp_client.py
```

Make sure you have `sample.wav` (and optionally `sample.mp4`) in the project directory.

## Configuration

Edit `config.py` to customize:

- **Server settings**: Host, port, buffer sizes
- **Audio settings**: Sample rates, chunk sizes
- **Video settings**: FPS, codecs, bitrates
- **File paths**: Directory structure for recordings
- **API credentials**: Gemini and Cloudflare tokens

## File Structure

```
├── mvp.py                 # Main WebSocket server
├── mvp_client.py          # Test client
├── config.py              # Configuration
├── video_processor.py     # Video processing logic
├── cloudflare_stream.py   # Cloudflare Stream integration
├── buffer_manager.py      # Buffering and synchronization
├── main.py               # FastAPI REST API (optional)
├── test_system.py        # System tests
├── recordings/           # Output directory
│   ├── original_audio/   # Original audio files
│   ├── original_video/   # Original video files
│   ├── translated_audio/ # Translated audio files
│   └── final_video/      # Final merged videos
└── temp/                 # Temporary files
```

## API Integration

### Gemini AI

- Handles real-time audio translation
- Supports multiple target languages
- Maintains audio duration consistency

### Cloudflare Stream

- Uploads final translated videos
- Provides streaming URLs
- Handles video encoding and delivery

## Development

### Running Tests

```bash
uv run python test_system.py
```

### Adding New Languages

1. Update the WebSocket endpoint routing in `mvp.py`
2. Add language-specific system instructions in the Gemini configuration

### Customizing Video Processing

- Modify `video_processor.py` for different video formats
- Adjust buffer sizes in `config.py` for performance tuning
- Update FFmpeg parameters for quality/compression settings

## Performance Considerations

- **Buffer Management**: System automatically adapts buffer sizes based on translation speed
- **Memory Usage**: Video frames are buffered in memory - adjust `VIDEO_BUFFER_SECONDS` for memory constraints
- **Network**: WebSocket connections handle real-time streaming - ensure adequate bandwidth
- **Processing**: Translation speed depends on Gemini API response times

## Troubleshooting

### Common Issues

1. **Import Errors**: Run `uv sync` to install dependencies
2. **Permission Errors**: Ensure write permissions for recording directories
3. **API Errors**: Check Gemini API key and Cloudflare credentials
4. **Memory Issues**: Reduce buffer sizes in configuration
5. **Network Issues**: Check WebSocket connection and firewall settings

### Logs

The system provides detailed logging. Set `LOG_LEVEL = "DEBUG"` in `config.py` for verbose output.

## System Requirements

- Python 3.12+
- FFmpeg (for video processing)
- Adequate memory for video buffering
- Network connectivity for API calls

## Current Status

✅ **Completed Features:**

- WebSocket server with multi-language support
- Audio translation via Gemini API
- Video processing and synchronization
- Adaptive buffering system
- Cloudflare Stream integration
- Comprehensive test suite

🚧 **Future Enhancements:**

- Real-time language switching during stream
- Enhanced video quality options
- Multiple simultaneous language outputs
- Advanced buffering strategies
- Performance monitoring dashboard
