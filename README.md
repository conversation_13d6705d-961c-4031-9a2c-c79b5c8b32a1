# Omnispeak Backend - Restructured

A clean, well-organized live video translation service with real-time speech processing.

## 🎯 What This App Does

Omnispeak is a **live video translation service** that:

1. **Receives live audio/video** from clients via WebSocket
2. **Transcribes speech** to text using Gladia STT or Gemini AI
3. **Translates text** between languages using Gemini AI with context awareness
4. **Converts translated text** back to speech using Google Cloud TTS
5. **Merges translated audio** with original video using FFmpeg
6. **Uploads final videos** to Cloudflare Stream for distribution

## 🏗️ Clean Architecture

The codebase has been **completely restructured** for better maintainability:

```
src/
├── api/                    # 🌐 API Layer
│   ├── main.py            # FastAPI app, routes, WebSocket endpoint
│   ├── websocket.py       # WebSocket message handling & callbacks
│   └── models.py          # Database models (SQLModel)
│
├── core/                  # 🧠 Core Business Logic
│   ├── pipeline.py        # Translation pipeline orchestration
│   └── session.py         # Session management & statistics
│
├── services/              # 🔌 External Service Integrations
│   ├── speech/
│   │   ├── stt.py         # Speech-to-text (Gladia, OpenA<PERSON> Whisper)
│   │   └── tts.py         # Text-to-speech (Google Cloud TTS)
│   ├── translation/
│   │   └── translator.py  # Text translation (Gemini AI)
│   ├── video/
│   │   └── processor.py   # Video processing (FFmpeg)
│   └── cloudflare/
│       └── stream.py      # Cloudflare Stream integration
│
└── utils/                 # 🛠️ Utilities & Helpers
    ├── config.py          # Configuration management
    ├── buffer.py          # Audio/video buffer management
    ├── chat.py            # Chat functionality
    └── sentence_buffer.py # Sentence detection & context
```

## 🔄 Data Flow

```
Client WebSocket
    ↓
Audio/Video Chunks
    ↓
Speech-to-Text (Gladia/Gemini)
    ↓
Sentence Detection & Context Building
    ↓
Translation (Gemini AI with context)
    ↓
Text-to-Speech (Google Cloud TTS)
    ↓
Audio Merging with Video (FFmpeg)
    ↓
Upload to Cloudflare Stream
```

## 🚀 Quick Start

### 1. Install Dependencies
```bash
uv sync
```

### 2. Set Environment Variables
```bash
export GEMINI_API_KEY="your_gemini_api_key"
export GLADIA_API_KEY="your_gladia_api_key"  # For real-time STT
export CLOUDFLARE_API_TOKEN="your_token"     # Optional
export CLOUDFLARE_ACCOUNT_ID="your_id"       # Optional
```

### 3. Run the Server
```bash
# Using the new clean entry point
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# Or directly with Python
python main.py
```

### 4. Test WebSocket Connection
```bash
# Connect to translation endpoint (replace 'en' with target language)
wscat -c ws://localhost:8000/ws/en
```

## 📁 Key Files Explained

### Entry Point
- **`main.py`** - Clean entry point that imports from the new structure

### API Layer (`src/api/`)
- **`main.py`** - FastAPI application with health checks and endpoints
- **`websocket.py`** - Handles WebSocket connections and real-time communication
- **`models.py`** - Database models for hosts, rooms, sessions

### Core Logic (`src/core/`)
- **`pipeline.py`** - Orchestrates the entire translation pipeline
- **`session.py`** - Manages session lifecycle and statistics

### Services (`src/services/`)
- **`speech/stt.py`** - Speech-to-text using Gladia (real-time) or OpenAI Whisper
- **`speech/tts.py`** - Text-to-speech using Google Cloud TTS
- **`translation/translator.py`** - Context-aware translation using Gemini AI
- **`video/processor.py`** - Video recording and audio merging with FFmpeg
- **`cloudflare/stream.py`** - Video upload to Cloudflare Stream

### Utilities (`src/utils/`)
- **`config.py`** - Centralized configuration management
- **`buffer.py`** - Audio/video buffering and synchronization
- **`chat.py`** - Chat functionality for rooms
- **`sentence_buffer.py`** - Intelligent sentence detection and context building

## 🔧 Configuration

All configuration is centralized in `src/utils/config.py`:

- **Audio Settings**: Sample rates, chunk sizes, codecs
- **Translation Settings**: Context sentences, timeouts, quality presets
- **Service Settings**: API keys, endpoints, retry logic
- **Performance Settings**: Latency modes (low_latency, balanced, high_quality)

## 🧪 Testing

```bash
# Test the restructured imports
python -c "import sys; sys.path.insert(0, 'src'); from src.api.main import app; print('✅ All imports working!')"

# Test WebSocket with the test client
python test_client.py
```

## 📊 What's Better Now

### Before (Old Structure)
- ❌ All code in root directory
- ❌ Mixed concerns in single files
- ❌ Hard to understand data flow
- ❌ Difficult to test individual components
- ❌ Import spaghetti

### After (New Structure)
- ✅ Clean separation of concerns
- ✅ Easy to understand each component's role
- ✅ Clear data flow through layers
- ✅ Testable individual modules
- ✅ Logical import hierarchy
- ✅ Professional project structure

## 🗂️ Old Files (Renamed)

All original files have been renamed with `to_be_deleted_` prefix:
- `to_be_deleted_main.py`
- `to_be_deleted_config.py`
- `to_be_deleted_speech_to_text.py`
- etc.

**These can be safely deleted once you confirm the new structure works.**

## 🎯 Next Steps

1. **Test the new structure** with your existing clients
2. **Update any external scripts** that import from the old files
3. **Delete the old files** once everything works
4. **Add unit tests** for individual components
5. **Consider adding type hints** throughout the codebase
