"""
Speech-to-Text module for live audio transcription using Gladia API
"""
import asyncio
import logging
import json
import uuid
from typing import Optional, Callable
import time
import config
from dataclasses import dataclass
import websockets
import ssl

logger = logging.getLogger(__name__)

# Global session manager to prevent concurrent Gladia sessions on free tier
class GladiaSessionManager:
    def __init__(self):
        self.active_sessions = set()
        self.max_concurrent = 1  # Free tier limit
    
    def can_create_session(self, session_id: str) -> bool:
        if len(self.active_sessions) >= self.max_concurrent:
            logger.warning(f"Cannot create Gladia session {session_id}: {len(self.active_sessions)}/{self.max_concurrent} sessions active")
            return False
        return True
    
    def register_session(self, session_id: str):
        self.active_sessions.add(session_id)
        logger.info(f"Registered Gladia session {session_id}. Active: {len(self.active_sessions)}")
    
    def unregister_session(self, session_id: str):
        self.active_sessions.discard(session_id)
        logger.info(f"Unregistered Gladia session {session_id}. Active: {len(self.active_sessions)}")

# Global instance
gladia_session_manager = GladiaSessionManager()

@dataclass
class TranscriptionResult:
    text: str
    is_final: bool
    confidence: float
    timestamp: float
    language: str

# Moved unused STT classes to trash.py for reference

class GladiaSTT:
    """
    Real-time Speech-to-Text using Gladia API with WebSocket streaming
    Provides <300ms latency real-time transcription in 100+ languages
    """
    
    def __init__(self, source_language: str = "auto", sample_rate: int = 16000, session_id: str = None):
        self.source_language = source_language
        self.sample_rate = sample_rate
        self.session_id = session_id or str(uuid.uuid4())
        
        # Initialize Gladia settings
        self.api_key = config.GLADIA_API_KEY
        self.base_url = "https://api.gladia.io"  # Fixed: Use HTTPS API base URL
        
        if not self.api_key:
            logger.warning("Gladia API key not found in environment variables")
            self.is_available = False
        elif not gladia_session_manager.can_create_session(self.session_id):
            logger.warning(f"Cannot create Gladia session {self.session_id}: rate limit reached")
            self.is_available = False
        else:
            self.is_available = True
            gladia_session_manager.register_session(self.session_id)
            logger.info("Gladia STT client initialized successfully")
            
        self.is_streaming = False
        self.websocket = None
        self.transcription_callback: Optional[Callable[[TranscriptionResult], None]] = None
        self.main_loop = None
        
        # Connection and streaming state
        self.connection_task = None
        self.audio_queue = asyncio.Queue()
        
    def set_transcription_callback(self, callback: Callable[[TranscriptionResult], None]):
        """Set callback function to receive transcription results"""
        self.transcription_callback = callback
        logger.info(f"[GLADIA-DEBUG] Setting Gladia transcription callback: {callback}")
        
    async def start_streaming_transcription(self) -> None:
        """Start real-time streaming transcription via WebSocket"""
        if not self.is_available:
            raise RuntimeError("Gladia STT not available - API key not configured")
            
        if self.is_streaming:
            logger.warning("Gladia STT already streaming")
            return
            
        try:
            # Store reference to the main event loop
            self.main_loop = asyncio.get_running_loop()
            logger.info("[GLADIA-DEBUG] Stored reference to main event loop")
        except RuntimeError:
            logger.warning("No running event loop found")
            
        self.is_streaming = True
        logger.info("Starting Gladia real-time STT streaming")
        
        # Start WebSocket connection
        self.connection_task = asyncio.create_task(self._websocket_handler())
        
    async def _websocket_handler(self):
        """Handle WebSocket connection and messaging"""
        
        # Fixed: First call the /v2/live endpoint to get WebSocket URL
        language_code = self._get_gladia_language_code(self.source_language)
        
        # Configure the live session
        config_data = {
            "encoding": "wav/pcm",
            "sample_rate": self.sample_rate,
            "bit_depth": 16,
            "channels": 1,
            "language_config": {
                "languages": [language_code] if language_code != "auto" else [],
                "code_switching": False
            },
            "realtime_processing": {
                "words_accurate_timestamps": True
            }
        }
        
        headers = {
            "Content-Type": "application/json",
            "x-gladia-key": self.api_key,
        }
        
        try:
            # Fixed: Call the API to get WebSocket URL
            import aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/v2/live",
                    headers=headers,
                    json=config_data
                ) as response:
                    if response.status != 201:
                        error_text = await response.text()
                        logger.error(f"Failed to create live session: {response.status} - {error_text}")
                        return
                    
                    result = await response.json()
                    websocket_url = result["url"]
                    session_id = result["id"]
                    
                    logger.info(f"[GLADIA-DEBUG] Got WebSocket URL: {websocket_url}")
                    logger.info(f"[GLADIA-DEBUG] Session ID: {session_id}")
            
            
            # Create SSL context for secure connection
            ssl_context = ssl.create_default_context()
            
            async with websockets.connect(
                websocket_url,
                ssl=ssl_context,
                ping_interval=20,
                ping_timeout=10
            ) as websocket:
                self.websocket = websocket
                logger.info("[GLADIA-DEBUG] WebSocket connected successfully")
                
                # Start audio sending task
                audio_task = asyncio.create_task(self._send_audio_loop())
                
                # Start message receiving task  
                receive_task = asyncio.create_task(self._receive_messages_loop())
                
                # Wait for either task to complete or fail
                done, pending = await asyncio.wait(
                    [audio_task, receive_task],
                    return_when=asyncio.FIRST_COMPLETED
                )
                
                # Cancel remaining tasks
                for task in pending:
                    task.cancel()
                    
        except Exception as e:
            logger.error(f"WebSocket connection error: {e}")
        finally:
            self.websocket = None
            logger.info("[GLADIA-DEBUG] WebSocket connection closed")
            
    async def _send_audio_loop(self):
        """Send audio data to Gladia WebSocket"""
        chunk_count = 0
        
        while self.is_streaming and self.websocket:
            try:
                # Get audio data from queue with timeout
                audio_data = await asyncio.wait_for(
                    self.audio_queue.get(), 
                    timeout=0.1
                )
                
                if audio_data is None:  # Sentinel value to stop
                    logger.info("[GLADIA-DEBUG] Received stop sentinel")
                    break
                    
                # Send raw audio data to Gladia (they expect raw binary)
                await self.websocket.send(audio_data)
                
                chunk_count += 1
                if chunk_count % 50 == 0:  # Log every 50 chunks to avoid spam
                    logger.info(f"[GLADIA-DEBUG] Sent audio chunk #{chunk_count}, size: {len(audio_data)}")
                    
            except asyncio.TimeoutError:
                # No audio data available, continue
                continue
            except Exception as e:
                logger.error(f"Error sending audio to Gladia: {e}")
                break
                
    async def _receive_messages_loop(self):
        """Receive and process messages from Gladia WebSocket"""
        message_count = 0
        
        while self.is_streaming and self.websocket:
            try:
                # Receive message from Gladia
                message = await self.websocket.recv()
                message_count += 1
                
                logger.info(f"[GLADIA-DEBUG] Received message #{message_count}")
                
                # Parse JSON response
                try:
                    data = json.loads(message)
                    await self._process_gladia_response(data)
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse Gladia response: {e}")
                    
            except websockets.exceptions.ConnectionClosed:
                logger.info("[GLADIA-DEBUG] WebSocket connection closed by server")
                break
            except Exception as e:
                logger.error(f"Error receiving from Gladia: {e}")
                break
                
    async def _process_gladia_response(self, data: dict):
        """Process transcription response from Gladia"""
        try:
            # Fixed: Handle Gladia V2 message format
            if data.get("type") == "transcript":
                transcript_data = data.get("data", {})
                utterance = transcript_data.get("utterance", {})
                text = utterance.get("text", "").strip()
                
                if text:
                    is_final = transcript_data.get("is_final", False)
                    confidence = utterance.get("confidence", 1.0)
                    
                    logger.info(f"[GLADIA-DEBUG] Transcript: '{text}', final: {is_final}, confidence: {confidence:.2f}")
                    
                    # Create transcription result
                    result = TranscriptionResult(
                        text=text,
                        is_final=is_final,
                        confidence=confidence,
                        timestamp=time.time(),
                        language=utterance.get("language", self.source_language)
                    )
                    
                    # Call the callback
                    if self.transcription_callback:
                        await self._schedule_callback(result)
                        
            elif data.get("type") == "error":
                error_msg = data.get("data", {}).get("message", "Unknown error")
                logger.error(f"[GLADIA-DEBUG] Error from Gladia: {error_msg}")
                
            else:
                logger.debug(f"[GLADIA-DEBUG] Received message type: {data.get('type')}")
                
        except Exception as e:
            logger.error(f"Error processing Gladia response: {e}")
            
    async def _schedule_callback(self, result: TranscriptionResult):
        """Schedule the callback in the main event loop"""
        if self.main_loop and self.transcription_callback:
            try:
                if asyncio.iscoroutinefunction(self.transcription_callback):
                    # Async callback - schedule it properly
                    future = asyncio.run_coroutine_threadsafe(
                        self.transcription_callback(result), 
                        self.main_loop
                    )
                    logger.info(f"[GLADIA-DEBUG] Successfully scheduled async callback")
                else:
                    # Sync callback - schedule in executor
                    future = self.main_loop.call_soon_threadsafe(
                        self.transcription_callback, result
                    )
                    logger.info(f"[GLADIA-DEBUG] Successfully scheduled sync callback")
            except Exception as e:
                logger.error(f"Error scheduling callback: {e}")
                
    def _get_gladia_language_code(self, source_language: str) -> str:
        """Convert source language to Gladia-compatible language code"""
        # Fixed: Use 2-letter language codes as per Gladia V2 API
        language_mapping = {
            "auto": "auto",
            "uk-UA": "uk",
            "uk": "uk", 
            "en-US": "en",
            "en": "en",
            "fr": "fr",
            "de": "de",
            "es": "es",
            "it": "it",
            "pt": "pt",
            "ru": "ru",
            "zh": "zh",
            "ja": "ja",
            "ko": "ko",
            "ar": "ar"
        }
        
        return language_mapping.get(source_language, "auto")
        
    async def add_audio_chunk(self, audio_data: bytes) -> None:
        """Add audio chunk to the streaming queue"""
        if self.is_streaming:
            try:
                await self.audio_queue.put(audio_data)
            except Exception as e:
                logger.warning(f"Failed to queue audio chunk: {e}")
                
    async def stop_streaming_transcription(self) -> None:
        """Stop streaming transcription"""
        logger.info("Stopping Gladia STT streaming")
        
        self.is_streaming = False
        
        # Send stop sentinel
        try:
            await self.audio_queue.put(None)
        except:
            pass
            
        # Send stop recording message to WebSocket
        if self.websocket:
            try:
                await self.websocket.send(json.dumps({
                    "type": "stop_recording"
                }))
                await self.websocket.close()
            except:
                pass
                
        # Cancel connection task
        if self.connection_task:
            self.connection_task.cancel()
            try:
                await self.connection_task
            except asyncio.CancelledError:
                pass
        
        # Unregister from session manager
        gladia_session_manager.unregister_session(self.session_id)
                
        logger.info("Stopped Gladia STT streaming")