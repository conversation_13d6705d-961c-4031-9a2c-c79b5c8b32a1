import asyncio
import os
import wave

import pyaudio
from google import genai
# Using the types module directly, which should be compatible with your library version
from google.genai import types

# --- Configuration ---
# Audio settings for the microphone
FORMAT = pyaudio.paInt16
CHANNELS = 1
RATE = 16000  # Gemini API requires 16kHz audio
CHUNK = 1024

# Output WAV file settings
OUTPUT_FILENAME = "translated_audio.wav"
OUTPUT_CHANNELS = 1
OUTPUT_RATE = 24000  # Gemini API returns audio at 24kHz
OUTPUT_SAMPLE_WIDTH = 2  # 16-bit audio

# --- Main Translation Logic ---

async def translate_voice():
    """
    Captures audio from the microphone, sends it to the Gemini API for translation,
    and saves the translated audio to a WAV file.
    """
    # --- Get Google API Key ---
    try:
        api_key = "AIzaSyCtWUcjDH-PaE3CuZuwDEzPnfWPDC_oQZk"
        client = genai.Client(api_key=api_key)
    except KeyError:
        print("Error: GOOGLE_API_KEY environment variable not set.")
        print("Please set your API key to run the program.")
        return

    # --- PyAudio Setup ---
    audio = pyaudio.PyAudio()
    stream = audio.open(
        format=FORMAT,
        channels=CHANNELS,
        rate=RATE,
        input=True,
        frames_per_buffer=CHUNK,
    )

    # --- Output WAV File Setup ---
    wave_file = wave.open(OUTPUT_FILENAME, "wb")
    wave_file.setnchannels(OUTPUT_CHANNELS)
    wave_file.setsampwidth(OUTPUT_SAMPLE_WIDTH)
    wave_file.setframerate(OUTPUT_RATE)

    # --- Gemini Live API Connection ---
    print("Connecting to Gemini API...")
    try:
        # Using RealtimeInputConfig as hinted by your working project's imports
        config = types.RealtimeInputConfig(
            model="models/gemini-2.5-flash-preview-native-audio-dialog",
            response_modality="AUDIO",
            speech_config=types.SpeechConfig(
                source_language="uk-UA",  # Ukrainian
                target_language="en-US",  # English
            ),
        )

        # Using the 'aot.connect' method which was used in earlier versions of the library
        async with client.aio.live.connect(config=config) as session:
            print("Connection successful. Start speaking Ukrainian.")
            print("Press Ctrl+C to stop.")

            try:
                while True:
                    # Read audio from microphone
                    data = stream.read(CHUNK, exception_on_overflow = False)

                    # Send audio to Gemini API
                    await session.send_audio(data)

                    # Receive translated audio
                    if session.responses:
                        for response in session.responses:
                            if response.audio:
                                wave_file.writeframes(response.audio)

            except KeyboardInterrupt:
                print("\nStopping the translation.")

    except AttributeError:
        print("\nError: It seems your 'google-generativeai' library is too old or the API has changed.")
        print("Please try updating the library with: pip install --upgrade google-generativeai")
    except Exception as e:
        print(f"An error occurred: {e}")

    finally:
        # --- Cleanup ---
        print("Cleaning up resources.")
        stream.stop_stream()
        stream.close()
        audio.terminate()
        wave_file.close()
        print(f"Translated audio saved to {OUTPUT_FILENAME}")


if __name__ == "__main__":
    asyncio.run(translate_voice())
