"""
Text-to-text translation module with context awareness
"""
import asyncio
import logging
import time
from typing import Optional, Callable, List
from dataclasses import dataclass
from google import genai
from google.genai import types
import config
from sentence_buffer import TranslationContext, Sentence

logger = logging.getLogger(__name__)

@dataclass
class TranslationResult:
    original_text: str
    translated_text: str
    target_language: str
    confidence: float
    timestamp: float
    context_used: List[str]

class TextTranslator:
    """
    Handles text-to-text translation with contextual awareness
    """
    
    def __init__(self, target_language: str, source_language: str = "auto"):
        self.target_language = target_language
        self.source_language = source_language
        self.client = genai.Client(api_key=config.GEMINI_API_KEY, http_options=types.HttpOptions(api_version="v1alpha"))
        self.translation_callback: Optional[Callable[[TranslationResult], None]] = None
        self.is_active = True
        
        # Enhanced translation quality settings
        self.use_context = True
        self.max_context_length = 800  # Increased from 500 for better context
        self.max_context_sentences = 5  # Increased from 3 for better narrative flow
        self.context_weight = 0.8  # How much to prioritize context vs. individual sentence
        
        # Track conversation themes and entities for better context
        self.conversation_themes = []
        self.mentioned_entities = set()  # People, places, things mentioned
        self.topic_continuity = []  # Track topic flow
        
        # Translation memory for consistency
        self.translation_memory = {}  # original -> translated mapping for consistency
        
    def set_translation_callback(self, callback: Callable[[TranslationResult], None]):
        """Set callback function to receive translation results"""
        self.translation_callback = callback
        
    async def translate_with_context(self, context: TranslationContext) -> None:
        """Enhanced translation with improved context handling"""
        if not self.is_active:
            return
            
        try:
            start_time = time.time()
            current_text = context.current_sentence.text.strip()
            
            # Prepare enhanced context
            context_text = self._prepare_context(context.previous_sentences)
            
            # Log context usage for debugging
            context_sentences_count = len(context.previous_sentences)
            logger.info(f"[TRANSLATION-CONTEXT] Using {context_sentences_count} previous sentences for context")
            if context_text:
                logger.info(f"[TRANSLATION-CONTEXT] Context preview: '{context_text[:100]}{'...' if len(context_text) > 100 else ''}'")
            
            # Create enhanced translation prompt
            prompt = self._create_translation_prompt(
                current_text,
                context_text,
                self.target_language,
                self.source_language
            )
            
            logger.info(f"[TRANSLATION] '{current_text}' -> {self.target_language} (with {context_sentences_count} context sentences)")
            
            # Perform translation
            response = await self._call_gemini_translation(prompt)
            
            if response and response.text:
                translated_text = self._extract_translation(response.text)
                
                # Update translation memory for consistency
                self._update_translation_memory(current_text, translated_text)
                
                # Calculate enhanced confidence
                confidence = self._calculate_enhanced_confidence(
                    context.current_sentence, 
                    translated_text, 
                    context_sentences_count
                )
                
                translation_result = TranslationResult(
                    original_text=current_text,
                    translated_text=translated_text,
                    target_language=self.target_language,
                    confidence=confidence,
                    timestamp=time.time(),
                    context_used=[s.text for s in context.previous_sentences]
                )
                
                # Enhanced logging
                elapsed_time = time.time() - start_time
                logger.info(f"[TRANSLATION] '{current_text}' -> '{translated_text}' (confidence: {confidence:.2f})")
                
                # Call callback
                if self.translation_callback:
                    if asyncio.iscoroutinefunction(self.translation_callback):
                        await self.translation_callback(translation_result)
                    else:
                        await asyncio.get_event_loop().run_in_executor(
                            None, self.translation_callback, translation_result
                        )
                
                logger.info(f"Translation completed in {elapsed_time:.2f}s: '{translated_text}'")
            else:
                logger.warning("Empty translation response from Gemini")
                
        except Exception as e:
            logger.error(f"Error in translation: {e}")
            
            # Create fallback result
            fallback_result = TranslationResult(
                original_text=context.current_sentence.text,
                translated_text=f"[Translation Error: {context.current_sentence.text}]",
                target_language=self.target_language,
                confidence=0.0,
                timestamp=time.time(),
                context_used=[]
            )
            
            if self.translation_callback:
                if asyncio.iscoroutinefunction(self.translation_callback):
                    await self.translation_callback(fallback_result)
                else:
                    await asyncio.get_event_loop().run_in_executor(
                        None, self.translation_callback, fallback_result
                    )
    
    def _prepare_context(self, previous_sentences: List[Sentence]) -> str:
        """Enhanced context preparation with better narrative coherence"""
        if not self.use_context or not previous_sentences:
            return ""
            
        # Build context with emphasis on recent and relevant sentences
        context_parts = []
        total_length = 0
        entity_mentions = []
        
        # Process sentences from most recent to oldest
        for sentence in reversed(previous_sentences[-self.max_context_sentences:]):
            sentence_text = sentence.text.strip()
            
            # Skip if adding this would exceed max length
            if total_length + len(sentence_text) > self.max_context_length:
                break
                
            # Track entities (simple pattern matching for now)
            entities = self._extract_entities(sentence_text)
            entity_mentions.extend(entities)
            
            context_parts.insert(0, sentence_text)
            total_length += len(sentence_text)
        
        # Update entity tracking
        self.mentioned_entities.update(entity_mentions)
        
        # Build narrative-aware context
        if context_parts:
            context = " ".join(context_parts)
            
            # Add conversation summary if we have a long context
            if len(context_parts) >= 3:
                topic_info = self._analyze_conversation_topic(context_parts)
                if topic_info:
                    context = f"[Topic: {topic_info}]\n{context}"
                    
            return context
        
        return ""
    
    def _extract_entities(self, text: str) -> List[str]:
        """Extract potential entities (names, places, etc.) from text"""
        import re
        entities = []
        
        # Simple pattern for capitalized words (potential names/places)
        # This is basic - could be enhanced with NER if needed
        words = text.split()
        for word in words:
            # Look for capitalized words that aren't sentence starters
            clean_word = re.sub(r'[^\w]', '', word)
            if (clean_word and clean_word[0].isupper() and len(clean_word) > 1 and 
                word != words[0]):  # Not the first word
                entities.append(clean_word)
                
        return entities
    
    def _analyze_conversation_topic(self, recent_sentences: List[str]) -> str:
        """Analyze recent sentences to determine conversation topic"""
        # Combine recent sentences and look for common themes
        combined_text = " ".join(recent_sentences).lower()
        
        # Simple keyword-based topic detection
        topic_keywords = {
            "technology": ["комп'ютер", "інтернет", "програма", "додаток", "технологія", "цифровий"],
            "business": ["бізнес", "робота", "компанія", "проект", "гроші", "продаж"],
            "education": ["школа", "університет", "навчання", "студент", "викладач", "урок"],
            "health": ["здоров'я", "лікар", "лікарня", "ліки", "хвороба", "медицина"],
            "family": ["сім'я", "мама", "тато", "дитина", "дім", "родина"],
            "travel": ["подорож", "країна", "місто", "літак", "готель", "відпустка"],
            "food": ["їжа", "ресторан", "кухня", "приготування", "смачно", "обід"],
            "weather": ["погода", "дощ", "сонце", "холодно", "тепло", "зима", "літо"]
        }
        
        topic_scores = {}
        for topic, keywords in topic_keywords.items():
            score = sum(1 for keyword in keywords if keyword in combined_text)
            if score > 0:
                topic_scores[topic] = score
                
        if topic_scores:
            return max(topic_scores.keys(), key=lambda k: topic_scores[k])
            
        return ""

    def _create_translation_prompt(self, text: str, context: str, target_lang: str, source_lang: str) -> str:
        """Create an enhanced translation prompt with better context utilization"""
        
        # Language code mapping for better prompts
        lang_names = {
            "en": "English",
            "uk": "Ukrainian", 
            "de": "German",
            "fr": "French",
            "es": "Spanish",
            "it": "Italian",
            "pt": "Portuguese",
            "ru": "Russian",
            "ja": "Japanese",
            "ko": "Korean",
            "zh": "Chinese",
            "ar": "Arabic"
        }
        
        target_lang_name = lang_names.get(target_lang, target_lang)
        source_lang_name = lang_names.get(source_lang, "the source language") if source_lang != "auto" else "the source language"
        
        # Check for entities that should be preserved
        mentioned_entities_str = ""
        if self.mentioned_entities:
            recent_entities = list(self.mentioned_entities)[-10:]  # Last 10 entities
            mentioned_entities_str = f"\nIMPORTANT ENTITIES TO MAINTAIN CONSISTENCY: {', '.join(recent_entities)}"
        
        # Check translation memory for consistency
        memory_hints = self._get_translation_memory_hints(text)
        memory_str = f"\nTRANSLATION CONSISTENCY: {memory_hints}" if memory_hints else ""
        
        if context:
            prompt = f"""You are a professional real-time translator providing live interpretation services. Your goal is to maintain conversational flow, consistency, and natural expression.

CONVERSATION CONTEXT (previous sentences for reference):
{context}

CURRENT SENTENCE TO TRANSLATE:
{text}

TARGET LANGUAGE: {target_lang_name}
SOURCE LANGUAGE: {source_lang_name}{mentioned_entities_str}{memory_str}

CRITICAL INSTRUCTIONS:
1. ONLY provide the translation - no explanations, prefixes, or additional text
2. Maintain narrative flow and logical connection with previous sentences
3. Use pronouns and references that make sense given the context
4. Preserve the speaker's tone, style, and level of formality established in context
5. Keep entity names (people, places, organizations) consistent with previous translations
6. If the current sentence continues a thought from context, translate accordingly
7. For incomplete thoughts, use context to infer intended meaning
8. Maintain temporal consistency (verb tenses that flow naturally)
9. Preserve cultural nuances and idiomatic expressions appropriately
10. If context suggests a specific domain (medical, technical, etc.), use appropriate terminology

Translation:"""
        else:
            prompt = f"""You are a professional real-time translator. Translate the following text from {source_lang_name} to {target_lang_name}.

SENTENCE TO TRANSLATE:
{text}{mentioned_entities_str}{memory_str}

INSTRUCTIONS:
1. ONLY provide the translation - no explanations or additional text
2. Maintain natural and fluent expression
3. Preserve the original meaning, tone, and style
4. Use appropriate formality level
5. If the text is incomplete or unclear, translate what you can understand logically
6. Keep proper nouns consistent unless there are standard translations
7. For incomplete thoughts, provide the most reasonable interpretation

Translation:"""
        
        return prompt
    
    def _get_translation_memory_hints(self, text: str) -> str:
        """Get translation hints from memory for consistency"""
        hints = []
        text_lower = text.lower()
        
        for original, translated in self.translation_memory.items():
            if original.lower() in text_lower:
                hints.append(f"'{original}' → '{translated}'")
                
        return "; ".join(hints[:3])  # Limit to top 3 hints
    
    async def _call_gemini_translation(self, prompt: str) -> Optional[types.GenerateContentResponse]:
        """Call Gemini API for translation"""
        try:
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.models.generate_content(
                    model="gemini-1.5-flash",
                    contents=[types.Content(parts=[types.Part(text=prompt)])],
                    config=types.GenerateContentConfig(
                        temperature=0.1,  # Low temperature for consistent translations
                        top_p=0.8,
                        candidate_count=1,
                        max_output_tokens=200,
                    )
                )
            )
            return response
        except Exception as e:
            logger.error(f"Gemini API error: {e}")
            return None
    
    def _extract_translation(self, response_text: str) -> str:
        """Extract clean translation from Gemini response"""
        # Remove common prefixes/suffixes that Gemini might add
        text = response_text.strip()
        
        # Remove common unwanted patterns
        patterns_to_remove = [
            r"^Translation:\s*",
            r"^Here's the translation:\s*",
            r"^The translation is:\s*",
            r"^\*\*Translation:\*\*\s*",
            r"^[\"']|[\"']$",  # Remove quotes at start/end
        ]
        
        import re
        for pattern in patterns_to_remove:
            text = re.sub(pattern, "", text, flags=re.IGNORECASE)
        
        return text.strip()
    
    def _update_translation_memory(self, original: str, translated: str) -> None:
        """Update translation memory for consistency"""
        # Store key phrases for consistency
        original = original.strip()
        translated = translated.strip()
        
        if len(original) > 2 and len(translated) > 2:
            # Store full sentence if short enough
            if len(original) <= 50:
                self.translation_memory[original] = translated
            
            # Extract and store key phrases/entities
            import re
            # Store capitalized entities
            entities = re.findall(r'\b[A-ZА-Я][a-zа-я]+(?:\s+[A-ZА-Я][a-zа-я]+)*\b', original)
            translated_entities = re.findall(r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b', translated)
            
            for i, entity in enumerate(entities):
                if i < len(translated_entities):
                    self.translation_memory[entity] = translated_entities[i]
        
        # Keep memory size manageable
        if len(self.translation_memory) > 100:
            # Remove oldest entries (simple FIFO)
            items = list(self.translation_memory.items())
            self.translation_memory = dict(items[-80:])  # Keep last 80 entries
    
    def _calculate_enhanced_confidence(self, original_sentence: Sentence, translated_text: str, context_count: int) -> float:
        """Calculate confidence with context factors"""
        base_confidence = original_sentence.confidence
        
        # Context bonus
        context_bonus = min(0.2, context_count * 0.05)  # Up to 20% bonus for good context
        
        # Length ratio factor
        length_ratio = len(translated_text) / max(len(original_sentence.text), 1)
        if 0.3 <= length_ratio <= 3.0:  # Reasonable length ratio
            length_bonus = 0.1
        else:
            length_bonus = -0.1
            
        # Completeness factor
        completeness_bonus = 0.1 if original_sentence.is_complete else -0.1
        
        # Translation memory consistency bonus
        memory_bonus = 0.05 if self._has_memory_consistency(original_sentence.text, translated_text) else 0
        
        # Calculate final confidence
        estimated_confidence = min(1.0, max(0.0, 
            base_confidence + context_bonus + length_bonus + completeness_bonus + memory_bonus
        ))
        
        return estimated_confidence
    
    def _has_memory_consistency(self, original: str, translated: str) -> bool:
        """Check if translation is consistent with memory"""
        original_lower = original.lower()
        for mem_original, mem_translated in self.translation_memory.items():
            if mem_original.lower() in original_lower and mem_translated.lower() in translated.lower():
                return True
        return False

    def _estimate_confidence(self, original_sentence: Sentence, translated_text: str) -> float:
        """Legacy confidence estimation method (kept for compatibility)"""
        return self._calculate_enhanced_confidence(original_sentence, translated_text, 0)
    
    async def translate_simple(self, text: str) -> Optional[str]:
        """Simple translation without context (for fallback use)"""
        try:
            prompt = f"Translate this text to {self.target_language}: {text}"
            response = await self._call_gemini_translation(prompt)
            
            if response and response.text:
                return self._extract_translation(response.text)
            return None
            
        except Exception as e:
            logger.error(f"Error in simple translation: {e}")
            return None
    
    def stop(self):
        """Stop the translator"""
        self.is_active = False
        logger.info("Text translator stopped")

class BatchTextTranslator:
    """
    Handles batch translation for better efficiency when multiple sentences are ready
    """
    
    def __init__(self, target_language: str, source_language: str = "auto", batch_size: int = 3):
        self.target_language = target_language
        self.source_language = source_language
        self.batch_size = batch_size
        self.client = genai.Client(api_key=config.GEMINI_API_KEY, http_options=types.HttpOptions(api_version="v1alpha"))
        self.pending_translations = []
        self.translation_callback: Optional[Callable[[List[TranslationResult]], None]] = None
        
    def set_translation_callback(self, callback: Callable[[List[TranslationResult]], None]):
        """Set callback function to receive batch translation results"""
        self.translation_callback = callback
        
    async def add_for_translation(self, context: TranslationContext) -> None:
        """Add a sentence context for batch translation"""
        self.pending_translations.append(context)
        
        # Process batch if we have enough items
        if len(self.pending_translations) >= self.batch_size:
            await self._process_batch()
    
    async def flush_batch(self) -> None:
        """Process any pending translations immediately"""
        if self.pending_translations:
            await self._process_batch()
            
    async def _process_batch(self) -> None:
        """Process a batch of translations"""
        if not self.pending_translations:
            return
            
        try:
            batch = self.pending_translations.copy()
            self.pending_translations.clear()
            
            # Create batch prompt
            prompt = self._create_batch_prompt(batch)
            
            # Perform batch translation
            response = await self._call_gemini_translation(prompt)
            
            if response and response.text:
                translations = self._parse_batch_response(response.text, batch)
                
                if self.translation_callback:
                    # Check if callback is async
                    if asyncio.iscoroutinefunction(self.translation_callback):
                        # Async callback - call directly
                        await self.translation_callback(translations)
                    else:
                        # Sync callback - use executor
                        await asyncio.get_event_loop().run_in_executor(
                            None, self.translation_callback, translations
                        )
            
        except Exception as e:
            logger.error(f"Error in batch translation: {e}")
            
    def _create_batch_prompt(self, contexts: List[TranslationContext]) -> str:
        """Create prompt for batch translation"""
        lang_names = {
            "en": "English", "uk": "Ukrainian", "de": "German", "fr": "French",
            "es": "Spanish", "it": "Italian", "pt": "Portuguese", "ru": "Russian",
            "ja": "Japanese", "ko": "Korean", "zh": "Chinese", "ar": "Arabic"
        }
        
        target_lang_name = lang_names.get(self.target_language, self.target_language)
        
        prompt = f"Translate these sentences to {target_lang_name}. Return only the translations, one per line:\n\n"
        
        for i, context in enumerate(contexts, 1):
            prompt += f"{i}. {context.current_sentence.text}\n"
            
        return prompt
        
    def _parse_batch_response(self, response_text: str, contexts: List[TranslationContext]) -> List[TranslationResult]:
        """Parse batch translation response"""
        lines = response_text.strip().split('\n')
        results = []
        
        for i, context in enumerate(contexts):
            if i < len(lines):
                translated_text = lines[i].strip()
                # Remove numbering if present
                import re
                translated_text = re.sub(r'^\d+\.\s*', '', translated_text)
            else:
                translated_text = f"[Translation missing for: {context.current_sentence.text}]"
                
            result = TranslationResult(
                original_text=context.current_sentence.text,
                translated_text=translated_text,
                target_language=self.target_language,
                confidence=context.current_sentence.confidence,
                timestamp=time.time(),
                context_used=[s.text for s in context.previous_sentences]
            )
            results.append(result)
            
        return results
        
    async def _call_gemini_translation(self, prompt: str) -> Optional[types.GenerateContentResponse]:
        """Call Gemini API for translation"""
        try:
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.models.generate_content(
                    model="gemini-1.5-flash",
                    contents=[types.Content(parts=[types.Part(text=prompt)])],
                    config=types.GenerateContentConfig(
                        temperature=0.1,
                        top_p=0.8,
                        candidate_count=1,
                        max_output_tokens=500,
                    )
                )
            )
            return response
        except Exception as e:
            logger.error(f"Gemini API error in batch translation: {e}")
            return None 