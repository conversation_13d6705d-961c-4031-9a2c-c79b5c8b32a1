"""
Unused Speech-to-Text classes - moved here for potential future reference
"""
import asyncio
import logging
import threading
import queue
import json
import base64
from typing import Generator, Optional, Callable
from google.cloud import speech
import wave
import io
from dataclasses import dataclass
import time
import config
import openai
import os
from pathlib import Path
import websockets
import ssl

logger = logging.getLogger(__name__)

@dataclass
class TranscriptionResult:
    text: str
    is_final: bool
    confidence: float
    timestamp: float
    language: str

class SpeechToText:
    """
    Real-time speech-to-text transcription using Google Cloud Speech-to-Text
    """
    
    def __init__(self, source_language: str = "auto", sample_rate: int = 16000):
        self.source_language = source_language
        self.sample_rate = sample_rate
        try:
            self.client = speech.SpeechClient()
            self.is_available = True
            logger.info("Google Cloud STT client initialized successfully")
        except Exception as e:
            logger.warning(f"Google Cloud STT not available: {e}")
            self.client = None
            self.is_available = False
            
        self.is_streaming = False
        self.audio_queue = queue.Queue()
        self.transcription_callback: Optional[Callable[[TranscriptionResult], None]] = None
        self.streaming_thread = None
        
        # Store reference to main event loop for cross-thread async calls
        try:
            self.main_loop = asyncio.get_running_loop()
            logger.info(f"[STT-DEBUG] Stored reference to main event loop")
        except RuntimeError:
            self.main_loop = None
            logger.warning(f"[STT-DEBUG] No event loop running during STT init")
        
    def set_transcription_callback(self, callback: Callable[[TranscriptionResult], None]):
        """Set callback function to receive transcription results"""
        logger.info(f"[STT-DEBUG] Setting transcription callback: {callback}")
        self.transcription_callback = callback
        logger.info(f"[STT-DEBUG] Transcription callback set successfully: {self.transcription_callback is not None}")
        
    async def start_streaming_transcription(self) -> None:
        """Start streaming transcription"""
        if self.is_streaming or not self.is_available:
            return
            
        self.is_streaming = True
        logger.info("Starting streaming speech-to-text transcription")
        
        # Start the streaming thread
        self.streaming_thread = threading.Thread(target=self._streaming_worker)
        self.streaming_thread.daemon = True
        self.streaming_thread.start()
        
    def _streaming_worker(self):
        """Worker thread for Google Cloud STT streaming"""
        try:
            logger.info("Starting Google Cloud STT streaming worker")
            logger.info(f"[STT-CONFIG] Sample rate: {self.sample_rate}Hz, Language: {self.source_language}")
            
            # Configure streaming recognition
            config_obj = speech.RecognitionConfig(
                encoding=speech.RecognitionConfig.AudioEncoding.LINEAR16,
                sample_rate_hertz=self.sample_rate,
                language_code=self.source_language if self.source_language != "auto" else "en-US",
                enable_automatic_punctuation=True,
                enable_word_time_offsets=False,
                model="latest_long",
                use_enhanced=True,
            )
            
            streaming_config = speech.StreamingRecognitionConfig(
                config=config_obj,
                interim_results=True,
                single_utterance=False,
            )
            
            logger.info(f"[STT-CONFIG] Recognition config: encoding=LINEAR16, sample_rate={self.sample_rate}Hz, language={self.source_language}")
            
            # Create the streaming recognition request
            audio_generator = self._audio_generator()
            requests = (speech.StreamingRecognizeRequest(audio_content=chunk)
                       for chunk in audio_generator)
            
            # Start streaming recognition
            logger.info("Starting Google Cloud STT streaming recognition")
            responses = self.client.streaming_recognize(streaming_config, requests)
            
            # Process responses
            response_count = 0
            for response in responses:
                response_count += 1
                logger.info(f"[STT-DEBUG] Received response #{response_count}")
                
                if not self.is_streaming:
                    logger.info("[STT-DEBUG] Stopping - is_streaming is False")
                    break
                    
                if not response.results:
                    logger.info("[STT-DEBUG] Response has no results")
                    continue
                    
                logger.info(f"[STT-DEBUG] Response has {len(response.results)} results")
                result = response.results[0]
                if not result.alternatives:
                    logger.info("[STT-DEBUG] Result has no alternatives")
                    continue
                    
                logger.info(f"[STT-DEBUG] Result has {len(result.alternatives)} alternatives")
                alternative = result.alternatives[0]
                
                logger.info(f"[STT-DEBUG] Transcript: '{alternative.transcript}', is_final: {result.is_final}")
                
                transcription_result = TranscriptionResult(
                    text=alternative.transcript,
                    is_final=result.is_final,
                    confidence=alternative.confidence if hasattr(alternative, 'confidence') else 0.0,
                    timestamp=time.time(),
                    language=self.source_language
                )
                
                # Schedule callback in the event loop
                if self.transcription_callback:
                    logger.info(f"[CALLBACK-DEBUG] Attempting to call transcription callback for: '{alternative.transcript}'")
                    try:
                        # Check if callback is async
                        if asyncio.iscoroutinefunction(self.transcription_callback):
                            logger.info(f"[CALLBACK-DEBUG] Callback is async")
                            
                            # Use stored main loop reference
                            if self.main_loop and not self.main_loop.is_closed():
                                logger.info(f"[CALLBACK-DEBUG] Using stored main loop")
                                future = asyncio.run_coroutine_threadsafe(
                                    self.transcription_callback(transcription_result),
                                    self.main_loop
                                )
                                logger.info(f"[CALLBACK-DEBUG] Successfully scheduled async callback")
                            else:
                                logger.error(f"[CALLBACK-DEBUG] Main loop not available or closed")
                        else:
                            # Sync callback - call directly
                            logger.info(f"[CALLBACK-DEBUG] Callback is sync, calling directly")
                            self.transcription_callback(transcription_result)
                            
                    except Exception as e:
                        logger.error(f"[CALLBACK-DEBUG] Exception in callback execution: {e}")
                else:
                    logger.warning(f"[CALLBACK-DEBUG] No transcription callback set!")
                    
        except Exception as e:
            logger.error(f"Error in streaming transcription worker: {e}")
        finally:
            self.is_streaming = False
            
    async def _call_transcription_callback(self, transcription_result: TranscriptionResult):
        """Helper to call transcription callback in async context"""
        if self.transcription_callback:
            await asyncio.get_event_loop().run_in_executor(
                None, self.transcription_callback, transcription_result
            )
            
    def _audio_generator(self) -> Generator[bytes, None, None]:
        """Generate audio chunks for streaming (synchronous generator)"""
        chunk_size = int(self.sample_rate * 0.1)  # 100ms chunks
        silence_chunk = b'\x00' * (chunk_size * 2)  # 16-bit silence
        
        chunk_count = 0
        silence_count = 0
        logger.info(f"[AUDIO-DEBUG] Starting audio generator, chunk_size: {chunk_size}")
        
        while self.is_streaming:
            try:
                # Get audio chunk from queue with timeout
                chunk = self.audio_queue.get(timeout=0.05)  # Shorter timeout
                if chunk is None:  # Sentinel value to stop
                    logger.info("[AUDIO-DEBUG] Received stop sentinel")
                    break
                chunk_count += 1
                if chunk_count % 50 == 0:  # Log every 50 chunks to avoid spam
                    logger.info(f"[AUDIO-DEBUG] Yielded audio chunk #{chunk_count}, size: {len(chunk)}")
                yield chunk
            except queue.Empty:
                # Yield silence to maintain continuous stream for Google Cloud STT
                silence_count += 1
                if silence_count % 100 == 0:  # Log every 100 silence chunks
                    logger.info(f"[AUDIO-DEBUG] Yielded silence chunk #{silence_count}")
                yield silence_chunk
                
    async def add_audio_chunk(self, audio_data: bytes) -> None:
        """Add audio chunk to the transcription buffer"""
        if self.is_streaming:
            try:
                self.audio_queue.put_nowait(audio_data)
            except queue.Full:
                logger.warning("Audio queue full, dropping chunk")
                
    async def stop_streaming_transcription(self) -> None:
        """Stop streaming transcription"""
        self.is_streaming = False
        
        # Send sentinel value to stop the generator
        try:
            self.audio_queue.put_nowait(None)
        except queue.Full:
            pass
            
        # Wait for streaming thread to finish
        if self.streaming_thread and self.streaming_thread.is_alive():
            self.streaming_thread.join(timeout=2.0)
            
        # Clear the queue
        while not self.audio_queue.empty():
            try:
                self.audio_queue.get_nowait()
            except queue.Empty:
                break
                
        logger.info("Stopped streaming speech-to-text transcription")

class FallbackSpeechToText:
    """
    Fallback STT implementation using Gemini for when Google Cloud STT is not available
    """
    
    def __init__(self, source_language: str = "auto", sample_rate: int = 16000):
        self.source_language = source_language
        self.sample_rate = sample_rate
        self.is_streaming = False
        self.audio_buffer = bytearray()
        self.transcription_callback: Optional[Callable[[TranscriptionResult], None]] = None
        self.accumulated_audio = bytearray()
        
    def set_transcription_callback(self, callback: Callable[[TranscriptionResult], None]):
        """Set callback function to receive transcription results"""
        self.transcription_callback = callback
        
    async def start_streaming_transcription(self) -> None:
        """Start streaming transcription using Gemini"""
        if self.is_streaming:
            return
            
        self.is_streaming = True
        logger.info("Starting fallback streaming speech-to-text using Gemini")
        
        # Start processing task
        asyncio.create_task(self._process_audio_chunks())
        
    async def _process_audio_chunks(self):
        """Process audio chunks periodically for transcription"""
        from google import genai
        from google.genai import types
        
        client = genai.Client(api_key=config.GEMINI_API_KEY, http_options=types.HttpOptions(api_version="v1alpha"))
        
        while self.is_streaming:
            await asyncio.sleep(2.0)  # Process every 2 seconds
            
            if len(self.accumulated_audio) < self.sample_rate * 1:  # At least 1 second of audio
                continue
                
            try:
                # Convert audio to WAV format
                audio_data = bytes(self.accumulated_audio)
                wav_buffer = io.BytesIO()
                
                with wave.open(wav_buffer, 'wb') as wav_file:
                    wav_file.setnchannels(1)
                    wav_file.setsampwidth(2)
                    wav_file.setframerate(self.sample_rate)
                    wav_file.writeframes(audio_data)
                
                wav_data = wav_buffer.getvalue()
                
                # Use Gemini for transcription
                response = await asyncio.get_event_loop().run_in_executor(
                    None,
                    lambda: client.models.generate_content(
                        model="gemini-1.5-flash",
                        contents=[
                            types.Content(parts=[
                                types.Part(text="Transcribe this audio to text. Only return the transcribed text, no other commentary."),
                                types.Part(inline_data=types.Blob(data=wav_data, mime_type="audio/wav"))
                            ])
                        ]
                    )
                )
                
                if response and response.text:
                    transcription_result = TranscriptionResult(
                        text=response.text.strip(),
                        is_final=True,
                        confidence=0.8,  # Estimated confidence
                        timestamp=time.time(),
                        language=self.source_language
                    )
                    
                    if self.transcription_callback:
                        await asyncio.get_event_loop().run_in_executor(
                            None, self.transcription_callback, transcription_result
                        )
                
                # Clear processed audio
                self.accumulated_audio.clear()
                
            except Exception as e:
                logger.error(f"Error in fallback transcription: {e}")
                
    async def add_audio_chunk(self, audio_data: bytes) -> None:
        """Add audio chunk to the transcription buffer"""
        if self.is_streaming:
            self.accumulated_audio.extend(audio_data)
            
            # Keep only last 10 seconds to prevent memory issues
            max_samples = self.sample_rate * 2 * 10  # 10 seconds of 16-bit audio
            if len(self.accumulated_audio) > max_samples:
                self.accumulated_audio = self.accumulated_audio[-max_samples:]
                
    async def stop_streaming_transcription(self) -> None:
        """Stop streaming transcription"""
        self.is_streaming = False
        self.accumulated_audio.clear()
        logger.info("Stopped fallback streaming speech-to-text transcription")


class OpenAIWhisperSTT:
    """
    Enhanced Speech-to-Text using OpenAI Whisper API for better punctuation and accuracy
    """
    
    def __init__(self, source_language: str = "auto", sample_rate: int = 16000):
        self.source_language = source_language
        self.sample_rate = sample_rate
        
        # Initialize OpenAI client
        try:
            # Try to use OpenAI API key from environment
            api_key = os.getenv('OPENAI_API_KEY') or getattr(config, 'OPENAI_API_KEY', None)
            if api_key:
                self.client = openai.OpenAI(api_key=api_key)
                self.is_available = True
                logger.info("OpenAI Whisper STT client initialized successfully")
            else:
                raise ValueError("No OpenAI API key found")
        except Exception as e:
            logger.warning(f"OpenAI Whisper STT not available: {e}")
            self.client = None
            self.is_available = False
            
        self.is_streaming = False
        self.audio_buffer = bytearray()
        self.transcription_callback: Optional[Callable[[TranscriptionResult], None]] = None
        self.processing_thread = None
        self.main_loop = None
        
        # Buffer settings for chunked processing
        self.chunk_duration = 3.0  # Process every 3 seconds for near real-time
        self.last_process_time = time.time()
        self.chunk_counter = 0
        
    def set_transcription_callback(self, callback: Callable[[TranscriptionResult], None]):
        """Set callback function to receive transcription results"""
        self.transcription_callback = callback
        logger.info(f"[WHISPER-DEBUG] Setting OpenAI Whisper transcription callback: {callback}")
        
    async def start_streaming_transcription(self) -> None:
        """Start streaming transcription (async interface to match other STT classes)"""
        success = self.start_streaming()
        if not success:
            raise RuntimeError("Failed to start OpenAI Whisper STT streaming")
        
    def start_streaming(self) -> bool:
        """Start the chunked audio processing"""
        if not self.is_available:
            logger.error("OpenAI Whisper STT not available")
            return False
            
        if self.is_streaming:
            logger.warning("OpenAI Whisper STT already streaming")
            return True
            
        try:
            # Store reference to the main event loop
            self.main_loop = asyncio.get_running_loop()
            logger.info("[WHISPER-DEBUG] Stored reference to main event loop")
        except RuntimeError:
            logger.warning("No running event loop found")
            
        self.is_streaming = True
        self.audio_buffer.clear()
        self.last_process_time = time.time()
        self.chunk_counter = 0
        
        # Start processing thread
        self.processing_thread = threading.Thread(target=self._processing_worker, daemon=True)
        self.processing_thread.start()
        
        logger.info("Started OpenAI Whisper STT chunked processing")
        return True
        
    async def add_audio_chunk(self, audio_data: bytes) -> None:
        """Add audio chunk to the buffer (async interface to match other STT classes)"""
        self.add_audio_data(audio_data)
        
    async def stop_streaming_transcription(self) -> None:
        """Stop streaming transcription (async interface to match other STT classes)"""
        self.stop_streaming()
        
    def stop_streaming(self):
        """Stop the chunked audio processing"""
        if not self.is_streaming:
            return
            
        self.is_streaming = False
        
        # Process any remaining audio
        if len(self.audio_buffer) > 0:
            self._process_audio_chunk(final=True)
            
        if self.processing_thread:
            self.processing_thread.join(timeout=5.0)
            
        logger.info("Stopped OpenAI Whisper STT processing")
        
    def add_audio_data(self, audio_data: bytes):
        """Add audio data to the buffer for processing"""
        if not self.is_streaming:
            return
            
        self.audio_buffer.extend(audio_data)
        
        # Check if it's time to process a chunk
        current_time = time.time()
        if current_time - self.last_process_time >= self.chunk_duration:
            self._trigger_chunk_processing()
            
    def _trigger_chunk_processing(self):
        """Trigger processing of the current audio buffer"""
        if len(self.audio_buffer) == 0:
            return
            
        # Process the chunk in the background
        threading.Thread(target=self._process_audio_chunk, daemon=True).start()
        self.last_process_time = time.time()
        
    def _processing_worker(self):
        """Background worker that periodically processes chunks"""
        while self.is_streaming:
            time.sleep(1.0)  # Check every second
            
            current_time = time.time()
            if (current_time - self.last_process_time >= self.chunk_duration and 
                len(self.audio_buffer) > 0):
                self._trigger_chunk_processing()
                
    def _process_audio_chunk(self, final: bool = False):
        """Process the current audio buffer with OpenAI Whisper"""
        if len(self.audio_buffer) == 0:
            return
            
        try:
            # Create audio chunk to process
            chunk_data = bytes(self.audio_buffer)
            self.chunk_counter += 1
            
            # Clear the buffer for new data (unless final processing)
            if not final:
                self.audio_buffer.clear()
            
            # Create temporary WAV file
            temp_path = Path(f"/tmp/whisper_chunk_{int(time.time() * 1000)}.wav")
            
            # Write audio data to WAV file
            with wave.open(str(temp_path), 'wb') as wav_file:
                wav_file.setnchannels(1)  # Mono
                wav_file.setsampwidth(2)  # 16-bit
                wav_file.setframerate(self.sample_rate)
                wav_file.writeframes(chunk_data)
                
            logger.info(f"[WHISPER-DEBUG] Processing chunk #{self.chunk_counter}, size: {len(chunk_data)} bytes")
            
            # Process with OpenAI Whisper
            with open(temp_path, "rb") as audio_file:
                transcript = self.client.audio.transcriptions.create(
                    model="whisper-1",
                    file=audio_file,
                    language=None if self.source_language == "auto" else self.source_language[:2],  # Use ISO 639-1 format
                    response_format="verbose_json"
                )
                
            # Clean up temp file
            temp_path.unlink()
            
            # Process the response
            if transcript.text.strip():
                logger.info(f"[WHISPER-DEBUG] Transcribed chunk #{self.chunk_counter}: '{transcript.text}'")
                
                # Create transcription result
                result = TranscriptionResult(
                    text=transcript.text.strip(),
                    is_final=True,  # Whisper results are always final
                    confidence=1.0,  # Whisper doesn't provide confidence scores
                    timestamp=time.time(),
                    language=transcript.language or self.source_language
                )
                
                # Call the callback
                if self.transcription_callback:
                    self._schedule_callback(result)
            else:
                logger.info(f"[WHISPER-DEBUG] Chunk #{self.chunk_counter} produced no transcription")
                    
        except Exception as e:
            logger.error(f"Error processing audio chunk with Whisper: {e}")
            
    def _schedule_callback(self, result: TranscriptionResult):
        """Schedule the callback in the main event loop"""
        if self.main_loop and self.transcription_callback:
            try:
                if asyncio.iscoroutinefunction(self.transcription_callback):
                    # Async callback - schedule it properly
                    future = asyncio.run_coroutine_threadsafe(
                        self.transcription_callback(result), 
                        self.main_loop
                    )
                    logger.info(f"[WHISPER-DEBUG] Successfully scheduled async callback")
                else:
                    # Sync callback - schedule in executor
                    future = self.main_loop.call_soon_threadsafe(
                        self.transcription_callback, result
                    )
                    logger.info(f"[WHISPER-DEBUG] Successfully scheduled sync callback")
            except Exception as e:
                logger.error(f"Error scheduling callback: {e}") 