"""
Live Translation Pipeline
Orchestrates the complete flow: Audio -> STT -> Sentence Detection -> Translation -> TTS -> Audio Output
"""
import asyncio
import logging
import time
from typing import Optional, Callable
from dataclasses import dataclass
from speech_to_text import GladiaSTT, TranscriptionResult
from sentence_buffer import <PERSON><PERSON><PERSON><PERSON>uff<PERSON>, TranslationContext
from text_translator import TextTranslator, TranslationResult
from text_to_speech import StreamingTextToSpeech, AudioResult
import config

logger = logging.getLogger(__name__)

@dataclass
class PipelineConfig:
    target_language: str
    source_language: str = "auto"
    max_context_sentences: int = 3
    sentence_timeout: float = 3.0
    use_gladia_stt: bool = True  # New option for real-time STT
    use_openai_whisper: bool = False  # Option for enhanced STT
    use_google_stt: bool = False
    use_google_tts: bool = True
    max_concurrent_tts: int = 3
    voice_gender: str = "NEUTRAL"
    speaking_rate: float = 1.0

class LiveTranslationPipeline:
    """
    Complete live translation pipeline
    """
    
    def __init__(self, session_id: str, config: PipelineConfig):
        self.session_id = session_id
        self.config = config
        self.is_active = False
        
        # Pipeline components
        self.stt: Optional[SpeechToText] = None
        self.sentence_buffer: Optional[SentenceBuffer] = None
        self.translator: Optional[TextTranslator] = None
        self.tts: Optional[StreamingTextToSpeech] = None
        
        # Callbacks
        self.audio_output_callback: Optional[Callable[[AudioResult], None]] = None
        self.transcription_callback: Optional[Callable[[TranscriptionResult], None]] = None
        self.translation_callback: Optional[Callable[[TranslationResult], None]] = None
        
        # Statistics
        self.stats = {
            "audio_chunks_received": 0,
            "transcriptions_processed": 0,
            "sentences_detected": 0,
            "translations_completed": 0,
            "audio_outputs_generated": 0,
            "total_latency_ms": 0,
            "start_time": None,
            "stt_type": None,
            "tts_type": None
        }
        
    async def start(self) -> None:
        """Start the translation pipeline"""
        if self.is_active:
            return
            
        logger.info(f"Starting live translation pipeline: {self.config.source_language} -> {self.config.target_language}")
        
        try:
            # Initialize STT with fallback
            await self._initialize_stt()
            
            # Initialize sentence buffer
            self.sentence_buffer = SentenceBuffer(
                max_context_sentences=self.config.max_context_sentences,
                sentence_timeout=self.config.sentence_timeout
            )
            
            # Initialize translator
            self.translator = TextTranslator(
                target_language=self.config.target_language,
                source_language=self.config.source_language
            )
            
            # Initialize TTS
            self.tts = StreamingTextToSpeech(
                target_language=self.config.target_language,
                max_concurrent=self.config.max_concurrent_tts
            )
            
            # Set up callbacks
            logger.info(f"[PIPELINE-DEBUG] Setting transcription callback: {self._on_transcription}")
            self.stt.set_transcription_callback(self._on_transcription)
            logger.info(f"[PIPELINE-DEBUG] STT callback set successfully")
            self.sentence_buffer.set_sentence_ready_callback(self._on_sentence_ready)
            self.translator.set_translation_callback(self._on_translation_ready)
            self.tts.set_audio_callback(self._on_audio_ready)
            
            # Start components
            await self.stt.start_streaming_transcription()
            
            self.is_active = True
            self.stats["start_time"] = time.time()
            
            logger.info(f"Live translation pipeline started successfully with {self.stats['stt_type']} STT")
            
        except Exception as e:
            logger.error(f"Failed to start pipeline: {e}")
            await self.stop()
            raise
    
    async def _initialize_stt(self):
        """Initialize STT with Gladia real-time streaming"""
        
        # Use Gladia STT for real-time transcription
        try:
            logger.info("Attempting to initialize Gladia STT...")
            self.stt = GladiaSTT(
                source_language=self.config.source_language,
                sample_rate=config.INPUT_SAMPLE_RATE,
                session_id=self.session_id
            )
            
            if self.stt.is_available:
                self.stats["stt_type"] = "Gladia STT"
                logger.info("✓ Gladia STT initialized successfully")
                return
            else:
                raise RuntimeError("Gladia STT not available - API key not configured")
                
        except Exception as e:
            logger.error(f"Gladia STT initialization failed: {e}")
            raise RuntimeError(f"Failed to initialize Gladia STT: {e}")
    
    async def stop(self) -> None:
        """Stop the translation pipeline"""
        if not self.is_active:
            return
            
        logger.info("Stopping live translation pipeline")
        
        self.is_active = False
        
        # Stop all components
        if self.stt:
            await self.stt.stop_streaming_transcription()
        
        if self.translator:
            self.translator.stop()
            
        if self.tts:
            await self.tts.stop()
            
        # Log final statistics
        self._log_final_stats()
        
        logger.info("Live translation pipeline stopped")
    
    async def add_audio_chunk(self, audio_data: bytes) -> None:
        """Add audio chunk to the STT buffer with quality monitoring"""
        if not self.is_active or not self.stt:
            return
            
        # Basic audio quality check
        if len(audio_data) > 0:
            # Convert bytes to samples for analysis (assuming 16-bit PCM)
            import struct
            try:
                # Unpack as 16-bit signed integers
                samples = struct.unpack(f'<{len(audio_data)//2}h', audio_data)
                
                # Calculate volume metrics
                max_amplitude = max(abs(s) for s in samples) / 32768.0  # Normalize to 0-1
                avg_amplitude = sum(abs(s) for s in samples) / len(samples) / 32768.0
                
                # Track audio quality
                if not hasattr(self, '_audio_stats'):
                    self._audio_stats = {
                        'chunks_processed': 0,
                        'total_max_amplitude': 0,
                        'low_volume_chunks': 0,
                        'last_warning_time': 0
                    }
                
                self._audio_stats['chunks_processed'] += 1
                self._audio_stats['total_max_amplitude'] += max_amplitude
                
                # Check if volume is too low for speech
                SPEECH_THRESHOLD = 0.01
                if max_amplitude < SPEECH_THRESHOLD:
                    self._audio_stats['low_volume_chunks'] += 1
                
                # Warn about low volume every 5 seconds
                import time
                current_time = time.time()
                if (current_time - self._audio_stats['last_warning_time'] > 5 and 
                    self._audio_stats['chunks_processed'] > 50):
                    
                    avg_volume = self._audio_stats['total_max_amplitude'] / self._audio_stats['chunks_processed']
                    low_volume_percentage = (self._audio_stats['low_volume_chunks'] / self._audio_stats['chunks_processed']) * 100
                    
                    if avg_volume < SPEECH_THRESHOLD * 2:  # Very low average
                        logger.warning(f"🔉 SERVER: Low audio volume detected - avg: {avg_volume:.4f}, {low_volume_percentage:.1f}% below speech threshold")
                        self._audio_stats['last_warning_time'] = current_time
                    elif low_volume_percentage > 80:  # Mostly silence
                        logger.info(f"📊 SERVER: Audio mostly silent - {low_volume_percentage:.1f}% below speech threshold")
                        self._audio_stats['last_warning_time'] = current_time
                
            except Exception as e:
                logger.debug(f"Audio quality analysis failed: {e}")
        
        await self.stt.add_audio_chunk(audio_data)
        self.stats["audio_chunks_received"] += 1
    
    def set_audio_output_callback(self, callback: Callable[[AudioResult], None]) -> None:
        """Set callback for audio output"""
        self.audio_output_callback = callback
        
    def set_transcription_callback(self, callback: Callable[[TranscriptionResult], None]) -> None:
        """Set callback for transcription events"""
        self.transcription_callback = callback
        
    def set_translation_callback(self, callback: Callable[[TranslationResult], None]) -> None:
        """Set callback for translation events"""
        self.translation_callback = callback
    
    async def _on_transcription(self, transcription: TranscriptionResult) -> None:
        """Handle transcription results"""
        logger.info(f"[PIPELINE-DEBUG] _on_transcription called with: '{transcription.text}', final: {transcription.is_final}")
        
        if not self.is_active:
            logger.warning(f"[PIPELINE-DEBUG] Pipeline not active, ignoring transcription: '{transcription.text}'")
            return
            
        self.stats["transcriptions_processed"] += 1
        
        logger.info(f"[STT] '{transcription.text}' (final: {transcription.is_final}, confidence: {transcription.confidence:.2f})")
        
        # Forward to external callback if set
        if self.transcription_callback:
            try:
                # Check if callback is async
                if asyncio.iscoroutinefunction(self.transcription_callback):
                    # Async callback - call directly
                    await self.transcription_callback(transcription)
                else:
                    # Sync callback - use executor
                    await asyncio.get_event_loop().run_in_executor(
                        None, self.transcription_callback, transcription
                    )
            except Exception as e:
                logger.error(f"Error in transcription callback: {e}")
        
        # Add to sentence buffer
        if self.sentence_buffer:
            await self.sentence_buffer.add_transcription(transcription)
    
    async def _on_sentence_ready(self, context: TranslationContext) -> None:
        """Handle sentence detection"""
        if not self.is_active:
            return
            
        self.stats["sentences_detected"] += 1
        
        logger.info(f"[SENTENCE] Ready for translation: '{context.current_sentence.text}' (context: {len(context.previous_sentences)} sentences)")
        
        # Send to translator
        if self.translator:
            await self.translator.translate_with_context(context)
    
    async def _on_translation_ready(self, translation: TranslationResult) -> None:
        """Handle translation completion"""
        if not self.is_active:
            return
            
        self.stats["translations_completed"] += 1
        
        logger.info(f"[TRANSLATION] '{translation.original_text}' -> '{translation.translated_text}' (confidence: {translation.confidence:.2f})")
        
        # Forward to external callback if set
        if self.translation_callback:
            try:
                # Check if callback is async
                if asyncio.iscoroutinefunction(self.translation_callback):
                    # Async callback - call directly
                    await self.translation_callback(translation)
                else:
                    # Sync callback - use executor
                    await asyncio.get_event_loop().run_in_executor(
                        None, self.translation_callback, translation
                    )
            except Exception as e:
                logger.error(f"Error in translation callback: {e}")
        
        # Send to TTS
        if self.tts:
            await self.tts.convert_to_speech(translation)
    
    async def _on_audio_ready(self, audio: AudioResult) -> None:
        """Handle TTS audio output"""
        if not self.is_active:
            return
            
        self.stats["audio_outputs_generated"] += 1
        
        logger.info(f"[TTS] Audio ready: {audio.duration_ms:.0f}ms for '{audio.text[:30]}...'")
        
        # Forward to external callback if set
        if self.audio_output_callback:
            try:
                # Check if callback is async
                if asyncio.iscoroutinefunction(self.audio_output_callback):
                    # Async callback - call directly
                    await self.audio_output_callback(audio)
                else:
                    # Sync callback - use executor
                    await asyncio.get_event_loop().run_in_executor(
                        None, self.audio_output_callback, audio
                    )
            except Exception as e:
                logger.error(f"Error in audio output callback: {e}")
    
    async def force_flush(self) -> None:
        """Force flush any pending sentences"""
        if self.sentence_buffer:
            await self.sentence_buffer.force_flush()
    
    def get_stats(self) -> dict:
        """Get pipeline statistics"""
        stats = self.stats.copy()
        if stats["start_time"]:
            stats["uptime_seconds"] = time.time() - stats["start_time"]
        return stats
    
    def _log_final_stats(self) -> None:
        """Log final pipeline statistics"""
        stats = self.get_stats()
        logger.info("Pipeline Statistics:")
        logger.info(f"  STT Type: {stats.get('stt_type', 'Unknown')}")
        logger.info(f"  Audio chunks received: {stats['audio_chunks_received']}")
        logger.info(f"  Transcriptions processed: {stats['transcriptions_processed']}")
        logger.info(f"  Sentences detected: {stats['sentences_detected']}")
        logger.info(f"  Translations completed: {stats['translations_completed']}")
        logger.info(f"  Audio outputs generated: {stats['audio_outputs_generated']}")
        logger.info(f"  Total uptime: {stats.get('uptime_seconds', 0):.1f}s")

    def log_statistics(self) -> None:
        """Log pipeline statistics with audio quality information"""
        audio_quality_info = ""
        if hasattr(self, '_audio_stats') and self._audio_stats['chunks_processed'] > 0:
            avg_volume = self._audio_stats['total_max_amplitude'] / self._audio_stats['chunks_processed']
            low_volume_percentage = (self._audio_stats['low_volume_chunks'] / self._audio_stats['chunks_processed']) * 100
            
            volume_status = "🟢 GOOD" if avg_volume > 0.05 else "🟡 LOW" if avg_volume > 0.01 else "🔴 TOO LOW"
            audio_quality_info = f"\n  Audio Quality: {volume_status} (avg: {avg_volume:.4f}, {low_volume_percentage:.1f}% silence)"
        
        logger.info(f"""
=== Translation Pipeline Statistics ===
  Session: {self.session_id}
  Audio chunks received: {self.stats["audio_chunks_received"]}{audio_quality_info}
  Transcriptions processed: {self.stats["transcriptions_processed"]}
  Sentences detected: {self.stats["sentences_detected"]}
  Translations completed: {self.stats["translations_completed"]}
  TTS audio generated: {self.stats["audio_outputs_generated"]}
  Status: {'🟢 Active' if self.is_active else '🔴 Inactive'}
=======================================
        """.strip())

class PipelineManager:
    """
    Manages multiple translation pipelines for different language pairs
    """
    
    def __init__(self):
        self.pipelines = {}
        self.is_active = False
        
    async def create_pipeline(self, session_id: str, pipeline_config: PipelineConfig) -> LiveTranslationPipeline:
        """Create a new translation pipeline for a session"""
        if session_id in self.pipelines:
            await self.stop_pipeline(session_id)
            
        pipeline = LiveTranslationPipeline(session_id, pipeline_config)
        self.pipelines[session_id] = pipeline
        
        logger.info(f"Created pipeline for session {session_id}: {pipeline_config.source_language} -> {pipeline_config.target_language}")
        
        return pipeline
    
    async def start_pipeline(self, session_id: str) -> None:
        """Start a pipeline"""
        if session_id in self.pipelines:
            await self.pipelines[session_id].start()
        else:
            raise ValueError(f"No pipeline found for session {session_id}")
    
    async def stop_pipeline(self, session_id: str) -> None:
        """Stop and remove a pipeline"""
        if session_id in self.pipelines:
            await self.pipelines[session_id].stop()
            del self.pipelines[session_id]
            logger.info(f"Stopped and removed pipeline for session {session_id}")
    
    def get_pipeline(self, session_id: str) -> Optional[LiveTranslationPipeline]:
        """Get a pipeline by session ID"""
        return self.pipelines.get(session_id)
    
    async def stop_all_pipelines(self) -> None:
        """Stop all active pipelines"""
        for session_id in list(self.pipelines.keys()):
            await self.stop_pipeline(session_id)
    
    def get_active_sessions(self) -> list:
        """Get list of active session IDs"""
        return list(self.pipelines.keys())
    
    def get_total_stats(self) -> dict:
        """Get aggregated statistics from all pipelines"""
        total_stats = {
            "active_pipelines": len(self.pipelines),
            "total_audio_chunks": 0,
            "total_transcriptions": 0,
            "total_sentences": 0,
            "total_translations": 0,
            "total_audio_outputs": 0,
            "stt_types": {}
        }
        
        for pipeline in self.pipelines.values():
            stats = pipeline.get_stats()
            total_stats["total_audio_chunks"] += stats.get("audio_chunks_received", 0)
            total_stats["total_transcriptions"] += stats.get("transcriptions_processed", 0)
            total_stats["total_sentences"] += stats.get("sentences_detected", 0)
            total_stats["total_translations"] += stats.get("translations_completed", 0)
            total_stats["total_audio_outputs"] += stats.get("audio_outputs_generated", 0)
            
            # Count STT types
            stt_type = stats.get("stt_type", "Unknown")
            total_stats["stt_types"][stt_type] = total_stats["stt_types"].get(stt_type, 0) + 1
        
        return total_stats

# Global pipeline manager instance
pipeline_manager = PipelineManager() 