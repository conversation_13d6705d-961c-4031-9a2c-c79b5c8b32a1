# --- Updated imports and pipeline integration ---
import asyncio
import io
import wave
import time
import json
import uuid
from pathlib import Path
import logging
import re
from typing import Optional
from datetime import datetime
from google import genai
from google.genai import types
from google.genai.types import RealtimeInputConfig, ActivityHandling

from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.responses import JSONResponse
from fastapi import Request
from fastapi.middleware.cors import CORSMiddleware

from sqlmodel import Field, SQLModel, create_engine
from contextlib import asynccontextmanager

# Import your own modules
import config
from video_processor import VideoProcessor
from cloudflare_stream import CloudflareStreamClient
from buffer_manager import buffer_manager
import chat # Import the chat module

# Import new pipeline components
from live_translation_pipeline import pipeline_manager, PipelineConfig, LiveTranslationPipeline
from speech_to_text import TranscriptionResult
from text_translator import TranslationResult
from text_to_speech import AudioResult

# Logging setup
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format=config.LOG_FORMAT
)
logger = logging.getLogger(__name__)

# Initialization
config.ensure_directories()

# Global chat database instance
chat_db = chat.ChatDatabase()

# Global connection tracking to prevent rate limit issues
active_connections = set()
MAX_CONCURRENT_CONNECTIONS = 1  # Gladia STT only allows 1 concurrent session

# Database setup
DATABASE_URL = "sqlite:///./db.sqlite3"
engine = create_engine(DATABASE_URL, echo=True)

# Models (keep existing models)
class Host(SQLModel, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    full_name: str
    preferred_language: str
    voice_sample_url: Optional[str] = None

class Room(SQLModel, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    host_id: int = Field(foreign_key="host.id")
    title: str
    host_language: str
    max_languages: int = 1
    status: str = "created"  # created, live, ended

class Guest(SQLModel, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    display_name: Optional[str] = None
    room_id: int = Field(foreign_key="room.id")

class RoomLanguage(SQLModel, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    room_id: int = Field(foreign_key="room.id")
    language: str
    stream_url: Optional[str] = None
    cloudflare_video_uid: Optional[str] = None
    status: str = "pending"  # pending, processing, ready, error

class TranslationSession(SQLModel, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    session_id: str = Field(unique=True)
    room_id: int = Field(foreign_key="room.id")
    target_language: str
    original_audio_path: Optional[str] = None
    original_video_path: Optional[str] = None
    translated_audio_path: Optional[str] = None
    final_video_path: Optional[str] = None
    cloudflare_video_uid: Optional[str] = None
    status: str = "active"  # active, completed, error
    created_at: Optional[str] = None
    completed_at: Optional[str] = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    SQLModel.metadata.create_all(engine)
    yield
    # Cleanup pipelines on shutdown
    await pipeline_manager.stop_all_pipelines()

app = FastAPI(lifespan=lifespan, docs_url=None, redoc_url=None)

# Enable CORS for all origins and allow POST, OPTIONS methods
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/docs", include_in_schema=False)
def custom_scalar_docs(request: Request):
    from scalar_fastapi import get_scalar_api_reference
    return get_scalar_api_reference(
        openapi_url=app.openapi_url,
        title=app.title,
    )

# --- New pipeline-based handlers ---

async def handle_websocket_messages(websocket, session_id, pipeline, video_processor, buffer, original_wf):
    """
    Handles messages from WebSocket client using the new pipeline architecture.
    """
    logger.info("Started processing WebSocket messages with new pipeline...")

    try:
        while True:
            try:
                msg = await websocket.receive()
            except WebSocketDisconnect:
                logger.info("WebSocket disconnected by client.")
                break
            except Exception as e:
                logger.error(f"WebSocket receive error: {e}")
                break

            if msg["type"] == "websocket.disconnect":
                logger.info("WebSocket disconnect event received.")
                break
            elif msg["type"] == "websocket.receive":
                data = msg.get("text")
                if data is not None:
                    # JSON/text message
                    try:
                        payload = json.loads(data)
                        logger.info(f"Received JSON message type={payload.get('type')}")
                        await handle_json_message(payload, video_processor, pipeline)
                    except Exception as e:
                        logger.error(f"Error processing JSON message: {e}")
                else:
                    # Binary message (audio data)
                    bin_data = msg.get("bytes")
                    if bin_data is not None:
                        logger.info(f"[TRACE] Received binary audio message of size {len(bin_data)} bytes")
                        await handle_binary_message(bin_data, pipeline, video_processor, buffer, original_wf)
                    else:
                        logger.warning(f"[TRACE] Received message with no text or bytes: {msg}")
            else:
                logger.warning(f"Unknown WebSocket message type: {msg['type']}")

    except Exception as e:
        logger.error(f"Error in handle_websocket_messages: {e}")
    finally:
        logger.info("Finished processing WebSocket messages.")

async def handle_binary_message(data: bytes, pipeline: LiveTranslationPipeline, video_processor, buffer, original_wf):
    """Handle binary audio/video data"""
    if len(data) < 10000:
        # Audio data
        logger.info(f"[TRACE] Processing audio chunk of size {len(data)} bytes")
        
        # Add to pipeline for STT processing
        await pipeline.add_audio_chunk(data)
        
        # Still save original audio
        try:
            original_wf.writeframes(data)
        except Exception as e:
            logger.error(f"Failed to write to original audio WAV: {e}")
    else:
        # Video data
        logger.info(f"[TRACE] Received large binary chunk (likely video); size={len(data)} bytes")
        await buffer.add_video_chunk(data)
        await video_processor.add_video_frame(data)
        logger.info(f"Processed video frame of size {len(data)} bytes")

async def handle_json_message(data: dict, video_processor, pipeline: LiveTranslationPipeline):
    """Handle JSON control messages"""
    try:
        if data.get('type') == 'start_recording':
            width = data.get('width', 1280)
            height = data.get('height', 720)
            await video_processor.start_recording(width, height)
            logger.info(f"Started video recording {width}x{height}")
            
        elif data.get('type') == 'stop_recording':
            await video_processor.stop_recording()
            logger.info("Stopped video recording")
            
        elif data.get('type') == 'join-room':
            room_id = data.get('roomId')
            role = data.get('role', 'guest')
            audio_config = data.get('audioConfig', {})
            
            logger.info(f"Client joined room: {room_id}, role: {role}")
            
            if audio_config:
                client_sample_rate = audio_config.get('sampleRate')
                channel_count = audio_config.get('channelCount', 1)
                encoding = audio_config.get('encoding', 'pcm16')
                
                logger.info(f"Client audio config: sample_rate={client_sample_rate}Hz, channels={channel_count}, encoding={encoding}")
                
                # Log if there's a sample rate mismatch
                if client_sample_rate and client_sample_rate != config.INPUT_SAMPLE_RATE:
                    logger.warning(f"⚠️ Sample rate mismatch: client={client_sample_rate}Hz, server expects={config.INPUT_SAMPLE_RATE}Hz")
                    logger.warning("This may cause transcription issues. Consider updating client or server configuration.")
            
        elif data.get('type') == 'end_of_turn':
            logger.info("Received end_of_turn control message; flushing pipeline...")
            # Force flush any pending sentences in the pipeline
            await pipeline.force_flush()
            
        elif data.get('type') == 'chat_message':
            # Handle chat messages
            message_text = data.get('text')
            room_id = data.get('room_id')
            sender_id = data.get('sender_id')
            
            if message_text and room_id and sender_id:
                chat_message = chat.ChatMessage(
                    text=message_text,
                    sender_id=sender_id,
                    timestamp=datetime.now(),
                    room_id=room_id
                )
                chat_db.save_message(chat_message)
                logger.info(f"Chat message saved to DB: {message_text}")
            else:
                logger.warning("Invalid chat message payload received.")

    except Exception as e:
        logger.error(f"Error processing JSON message: {e}")

# --- Pipeline callback handlers ---

class WebSocketCallbacks:
    """Handles callbacks from the pipeline and sends data back to WebSocket"""
    
    def __init__(self, websocket: WebSocket, session_id: str):
        self.websocket = websocket
        self.session_id = session_id
        self.translated_audio_buffer = bytearray()
        
    async def on_transcription(self, transcription: TranscriptionResult):
        """Handle transcription results"""
        try:
            # Send transcription to client
            await self.websocket.send_json({
                "type": "transcription",
                "text": transcription.text,
                "is_final": transcription.is_final,
                "confidence": transcription.confidence,
                "timestamp": transcription.timestamp
            })
        except Exception as e:
            logger.error(f"Error sending transcription to WebSocket: {e}")
    
    async def on_translation(self, translation: TranslationResult):
        """Handle translation results"""
        try:
            # Send translation to client
            await self.websocket.send_json({
                "type": "translation",
                "original_text": translation.original_text,
                "translated_text": translation.translated_text,
                "target_language": translation.target_language,
                "confidence": translation.confidence,
                "timestamp": translation.timestamp
            })
        except Exception as e:
            logger.error(f"Error sending translation to WebSocket: {e}")
    
    async def on_audio_output(self, audio: AudioResult):
        """Handle TTS audio output"""
        try:
            # Buffer the audio data
            self.translated_audio_buffer.extend(audio.audio_data)
            
            # Send audio data to client as binary
            await self.websocket.send_bytes(audio.audio_data)
            
            logger.info(f"Sent {len(audio.audio_data)} bytes of translated audio to client")
            
            # Save individual translated audio chunks for debugging
            if hasattr(self, 'session_id'):
                await self._save_translated_audio_chunk(audio)
                
        except Exception as e:
            logger.error(f"Error sending audio to WebSocket: {e}")
            
    async def _save_translated_audio_chunk(self, audio: AudioResult):
        """Save individual translated audio chunk"""
        try:
            timestamp = int(time.time() * 1000)  # milliseconds for uniqueness
            safe_session_id = re.sub(r'[^\w\.\-]', '_', self.session_id)
            chunk_path = Path("recordings/translated_audio") / f"chunk_{safe_session_id}_{timestamp}.wav"
            
            # Ensure directory exists
            chunk_path.parent.mkdir(parents=True, exist_ok=True)
            
            with wave.open(str(chunk_path), "wb") as wf:
                wf.setnchannels(1)
                wf.setsampwidth(2)
                wf.setframerate(config.OUTPUT_SAMPLE_RATE)
                wf.writeframes(audio.audio_data)
                
            logger.info(f"Saved translated audio chunk: {chunk_path} ({len(audio.audio_data)} bytes)")
        except Exception as e:
            logger.error(f"Error saving translated audio chunk: {e}")

async def finalize_video_processing(session_id, translated_audio_buffer, video_processor, cloudflare_client, target_lang):
    """Finalize video processing with the accumulated translated audio"""
    try:
        await video_processor.stop_recording()
        if not video_processor.original_video_path.exists():
            logger.warning("No video file found, skipping video processing")
            return
            
        if not translated_audio_buffer:
            logger.warning("No translated audio found, skipping video processing")
            return
            
        # Save accumulated translated audio to file
        timestamp = int(time.time())
        safe_session_id = re.sub(r'[^\w\.\-]', '_', session_id)
        translated_audio_path = Path(config.TRANSLATED_AUDIO_DIR) / f"translation_{target_lang}_{timestamp}_{safe_session_id}.wav"
        
        with wave.open(str(translated_audio_path), "wb") as wf:
            wf.setnchannels(1)
            wf.setsampwidth(2)
            wf.setframerate(config.OUTPUT_SAMPLE_RATE)
            wf.writeframes(bytes(translated_audio_buffer))
            
        logger.info(f"Saved accumulated translated audio: {translated_audio_path} ({len(translated_audio_buffer)} bytes)")
        
        # Merge with video
        final_video_path = await video_processor.merge_with_audio(translated_audio_path)
        logger.info(f"Video merged successfully: {final_video_path}")
        
        # Upload to Cloudflare if configured
        if cloudflare_client:
            try:
                video_name = f"translated_{target_lang}_{session_id}"
                upload_result = await cloudflare_client.upload_video(final_video_path, video_name)
                logger.info(f"Video uploaded to Cloudflare Stream: {upload_result.get('uid')}")
            except Exception as e:
                logger.error(f"Error uploading to Cloudflare Stream: {e}")
                
    except Exception as e:
        logger.error(f"Error in finalize_video_processing: {e}")

async def cleanup_session(session_id, video_processor, buffer):
    """Clean up session resources"""
    try:
        await video_processor.stop_recording()
        video_processor.cleanup()
        await buffer_manager.remove_buffer(session_id)
        
        # Stop pipeline
        await pipeline_manager.stop_pipeline(session_id)
        
        logger.info(f"Session {session_id} cleaned up successfully")
    except Exception as e:
        logger.error(f"Error cleaning up session {session_id}: {e}")

@app.websocket("/ws/{target_lang}")
async def websocket_endpoint(websocket: WebSocket, target_lang: str):
    await websocket.accept()
    session_id = str(uuid.uuid4())
    logger.info(f"New WebSocket connection. Translating to {target_lang}. Session: {session_id}")
    logger.info(f"Client address: {websocket.client}")
    
    # Check if we have too many concurrent connections
    if len(active_connections) >= MAX_CONCURRENT_CONNECTIONS:
        logger.warning(f"Rate limit: Too many concurrent connections ({len(active_connections)}/{MAX_CONCURRENT_CONNECTIONS}). Rejecting connection {session_id}")
        await websocket.close(code=1008, reason="Too many concurrent connections. Please try again later.")
        return
    
    # Add to active connections
    active_connections.add(session_id)
    logger.info(f"Added session {session_id} to active connections. Total: {len(active_connections)}")
    
    # Track session start time for debugging empty sessions
    session_start_time = time.time()
    
    # Create enhanced pipeline configuration with better context
    pipeline_config = PipelineConfig(
        target_language=target_lang,
        source_language="uk-UA",  # Ukrainian as source language
        max_context_sentences=5,  # Increased from 3 for better context
        sentence_timeout=1.8,  # Slightly longer for better sentence completion
        use_gladia_stt=True,  # Use Gladia for real-time STT
        use_openai_whisper=False,  # Disable OpenAI Whisper
        use_google_stt=False,  # Disable Google STT
        use_google_tts=True,
        max_concurrent_tts=2,
        voice_gender="NEUTRAL",
        speaking_rate=1.0
    )
    
    # Initialize components
    video_processor = VideoProcessor(session_id)
    buffer = await buffer_manager.create_buffer(session_id)
    cloudflare_client = None
    original_audio_path = Path(config.ORIGINAL_AUDIO_DIR) / f"{session_id}_original.wav"
    original_wf = wave.open(str(original_audio_path), "wb")
    original_wf.setnchannels(1)
    original_wf.setsampwidth(2)
    original_wf.setframerate(config.INPUT_SAMPLE_RATE)
    
    # Create and configure pipeline
    pipeline = await pipeline_manager.create_pipeline(session_id, pipeline_config)
    
    # Set up WebSocket callbacks
    ws_callbacks = WebSocketCallbacks(websocket, session_id)
    pipeline.set_transcription_callback(ws_callbacks.on_transcription)
    pipeline.set_translation_callback(ws_callbacks.on_translation)
    pipeline.set_audio_output_callback(ws_callbacks.on_audio_output)

    try:
        if config.CLOUDFLARE_API_TOKEN and config.CLOUDFLARE_ACCOUNT_ID:
            cloudflare_client = CloudflareStreamClient()

        # Start the pipeline
        await pipeline.start()
        logger.info("Translation pipeline started successfully")
        
        # Handle WebSocket messages
        await handle_websocket_messages(websocket, session_id, pipeline, video_processor, buffer, original_wf)

    except WebSocketDisconnect:
        logger.info(f"Client disconnected gracefully. Session: {session_id}")
    except Exception as e:
        logger.error(f"Error during WebSocket session {session_id}: {e}")
    finally:
        # Immediately stop pipeline to prevent continued processing
        try:
            await pipeline.stop()
            logger.info(f"Pipeline stopped for session {session_id}")
        except Exception as e:
            logger.error(f"Error stopping pipeline for session {session_id}: {e}")
        try:
            original_wf.close()
            logger.info(f"Original audio saved to {original_audio_path}")
        except Exception as e:
            logger.error(f"Error finalizing original audio file: {e}")
            
        # Always save accumulated translated audio if any exists
        if hasattr(ws_callbacks, 'translated_audio_buffer') and ws_callbacks.translated_audio_buffer:
            await finalize_video_processing(
                session_id, 
                ws_callbacks.translated_audio_buffer, 
                video_processor, 
                cloudflare_client, 
                target_lang
            )
        elif hasattr(ws_callbacks, 'translated_audio_buffer'):
            # Save even empty buffer for debugging
            timestamp = int(time.time())
            safe_session_id = re.sub(r'[^\w\.\-]', '_', session_id)
            translated_audio_path = Path("recordings/translated_audio") / f"final_{target_lang}_{timestamp}_{safe_session_id}.wav"
            translated_audio_path.parent.mkdir(parents=True, exist_ok=True)
            
            with wave.open(str(translated_audio_path), "wb") as wf:
                wf.setnchannels(1)
                wf.setsampwidth(2)
                wf.setframerate(config.OUTPUT_SAMPLE_RATE)
                if ws_callbacks.translated_audio_buffer:
                    wf.writeframes(bytes(ws_callbacks.translated_audio_buffer))
                    
            logger.info(f"Saved final translated audio: {translated_audio_path} ({len(ws_callbacks.translated_audio_buffer)} bytes)")
            
        await cleanup_session(session_id, video_processor, buffer)
        
        # Remove from active connections
        active_connections.discard(session_id)
        logger.info(f"Removed session {session_id} from active connections. Remaining: {len(active_connections)}")
        
        # Log session statistics for debugging
        session_duration = time.time() - session_start_time
        if hasattr(ws_callbacks, 'translated_audio_buffer'):
            audio_size = len(ws_callbacks.translated_audio_buffer)
        else:
            audio_size = 0
            
        logger.info(f"Connection with {websocket.client} closed. Session: {session_id}")
        logger.info(f"Session duration: {session_duration:.1f}s, Translated audio: {audio_size} bytes")

# --- Keep existing chat endpoint ---
@app.post("/chat")
async def post_chat_message(msg: chat.ChatMessageInc, request: Request):
    listener_id = chat.get_listener_id_from_jwt(request)
    chat_message = chat.ChatMessage(
        text=msg.text,
        sender_id=listener_id,
        timestamp=datetime.now(),
        room_id=msg.room_id
    )
    chat_db.save_message(chat_message)
    return JSONResponse({"status": "ok"})

# --- Add pipeline management endpoints ---
@app.get("/api/pipeline/stats")
async def get_pipeline_stats():
    """Get statistics for all active pipelines"""
    return JSONResponse(pipeline_manager.get_total_stats())

@app.get("/api/pipeline/sessions")
async def get_active_sessions():
    """Get list of active translation sessions"""
    return JSONResponse({"active_sessions": pipeline_manager.get_active_sessions()})

